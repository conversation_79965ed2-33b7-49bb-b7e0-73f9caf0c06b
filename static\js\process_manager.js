// Process and Package Management Module

let currentDevice = null;

// Process management functions
async function loadProcesses() {
    if (!currentDevice) return;

    const container = document.getElementById('processContainer');
    container.innerHTML = '<div class="loading">Loading processes...</div>';

    try {
        const showAll = document.getElementById('showAllProcesses').checked;
        const response = await fetch(`/processes?serial=${encodeURIComponent(currentDevice)}&show_all=${showAll}`);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Failed to load processes');
        }

        const processes = data.processes;

        if (processes.length === 0) {
            container.innerHTML = '<div class="loading">No processes found</div>';
            return;
        }

        let tableHTML = `
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Package Name</th>
                            <th>PID</th>
                            <th>User</th>
                            <th>Process Name</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        processes.forEach(process => {
            tableHTML += `
                <tr>
                    <td>${process.package_name || 'N/A'}</td>
                    <td>${process.pid}</td>
                    <td>${process.user}</td>
                    <td>${process.name}</td>
                    <td>
                        <div class="action-buttons">
                            ${process.package_name ? `
                                <button class="btn-info" onclick="getProcessInfo('${process.package_name}')">ℹ️ Info</button>
                                <button class="btn-danger" onclick="stopApplication('${process.package_name}')">⏹️ Stop</button>
                            ` : ''}
                            <button class="btn-danger" onclick="killProcess(${process.pid})">❌ Kill</button>
                        </div>
                    </td>
                </tr>
            `;
        });

        tableHTML += '</tbody></table></div>';
        container.innerHTML = tableHTML;

    } catch (error) {
        container.innerHTML = `<div class="loading">Error: ${error.message}</div>`;
        showStatus(`Error loading processes: ${error.message}`, 'error');
    }
}

async function getProcessInfo(packageName) {
    try {
        const response = await fetch(`/processes/info?serial=${encodeURIComponent(currentDevice)}&package_name=${encodeURIComponent(packageName)}`);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Failed to get process info');
        }

        let content = `
            <h4>⚙️ Process Information</h4>
            <div class="info-grid">
                <div class="info-item">
                    <strong>Package Name</strong>
                    ${data.package_name}
                </div>
                <div class="info-item">
                    <strong>Process ID</strong>
                    ${data.pid}
                </div>
        `;

        if (data.memory_info) {
            if (data.memory_info.pss_kb) {
                content += `
                    <div class="info-item">
                        <strong>Memory (PSS)</strong>
                        ${(data.memory_info.pss_kb / 1024).toFixed(1)} MB
                    </div>
                `;
            }
            if (data.memory_info.ram_kb) {
                content += `
                    <div class="info-item">
                        <strong>RAM Usage</strong>
                        ${(data.memory_info.ram_kb / 1024).toFixed(1)} MB
                    </div>
                `;
            }
        }

        content += '</div>';
        showModal('Process Information', content);

    } catch (error) {
        showStatus(`Error getting process info: ${error.message}`, 'error');
    }
}

async function stopApplication(packageName) {
    if (!confirm(`Are you sure you want to stop ${packageName}?`)) {
        return;
    }

    try {
        const response = await fetch('/processes/stop', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                serial: currentDevice,
                package_name: packageName
            })
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Failed to stop application');
        }

        showStatus(`Successfully stopped ${packageName}`, 'success');
        loadProcesses(); // Refresh the process list

    } catch (error) {
        showStatus(`Error stopping application: ${error.message}`, 'error');
    }
}

async function killProcess(pid) {
    if (!confirm(`Are you sure you want to kill process ${pid}?`)) {
        return;
    }

    try {
        const response = await fetch('/processes/kill', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                serial: currentDevice,
                pid: pid
            })
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Failed to kill process');
        }

        showStatus(`Successfully killed process ${pid}`, 'success');
        loadProcesses(); // Refresh the process list

    } catch (error) {
        showStatus(`Error killing process: ${error.message}`, 'error');
    }
}

// Package management functions
async function getPackageInfo() {
    const packageName = document.getElementById('packageName').value.trim();
    if (!packageName || !currentDevice) return;

    try {
        const response = await fetch(`/packages/info?serial=${encodeURIComponent(currentDevice)}&package_name=${encodeURIComponent(packageName)}`);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Failed to get package info');
        }

        let content = `<h4>📦 Package Information</h4><div class="info-grid">`;

        Object.entries(data).forEach(([key, value]) => {
            if (key !== 'permissions') {
                content += `
                    <div class="info-item">
                        <strong>${key.replace(/_/g, ' ').toUpperCase()}</strong>
                        ${Array.isArray(value) ? value.join(', ') : value}
                    </div>
                `;
            }
        });

        if (data.permissions && data.permissions.length > 0) {
            content += `
                <div class="info-item" style="grid-column: 1 / -1;">
                    <strong>PERMISSIONS</strong>
                    <div style="max-height: 200px; overflow-y: auto; margin-top: 10px;">
                        ${data.permissions.map(perm => `<div style="padding: 2px 0;">${perm}</div>`).join('')}
                    </div>
                </div>
            `;
        }

        content += '</div>';
        showModal('Package Information', content);

    } catch (error) {
        showStatus(`Error getting package info: ${error.message}`, 'error');
    }
}

async function downloadPackage() {
    const packageName = document.getElementById('packageName').value.trim();
    if (!packageName || !currentDevice) return;

    try {
        const response = await fetch('/packages/download', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                serial: currentDevice,
                package_name: packageName
            })
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Failed to download package');
        }

        showStatus(`Successfully downloaded ${packageName} (${(data.file_size / 1024 / 1024).toFixed(2)} MB)`, 'success');

    } catch (error) {
        showStatus(`Error downloading package: ${error.message}`, 'error');
    }
}

async function uninstallPackage() {
    const packageName = document.getElementById('packageName').value.trim();
    if (!packageName || !currentDevice) return;

    if (!confirm(`Are you sure you want to uninstall ${packageName}?`)) {
        return;
    }

    try {
        const response = await fetch('/packages/uninstall', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                serial: currentDevice,
                package_name: packageName
            })
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Failed to uninstall package');
        }

        showStatus(`Successfully uninstalled ${packageName}`, 'success');

    } catch (error) {
        showStatus(`Error uninstalling package: ${error.message}`, 'error');
    }
}

// UI helper functions
function showModal(title, content) {
    const modal = document.getElementById('modal');
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');
    
    modalTitle.textContent = title;
    modalBody.innerHTML = content;
    
    // Show the modal using Bootstrap's modal API
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}

function showStatus(message, type = 'info') {
    const statusContainer = document.getElementById('statusContainer');
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.role = 'alert';
    
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    statusContainer.appendChild(alertDiv);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
