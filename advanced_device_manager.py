"""
Advanced Device Manager for Android devices with Samsung-specific features
Includes MTP activation, FRP bypass, MDM management, and advanced device control
"""

import os
import subprocess
import time
import logging
import json
import re
import threading
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import winreg
import ctypes
from ctypes import wintypes

logger = logging.getLogger(__name__)

class ADBManager:
    """Advanced ADB Manager with enhanced device control capabilities."""
    
    def __init__(self, adb_path='adb'):
        self.adb_path = adb_path
        self.samsung_drivers_path = self._find_samsung_drivers()
        
    def _find_samsung_drivers(self):
        """Find Samsung USB drivers installation path."""
        try:
            # Check common Samsung driver locations
            common_paths = [
                r"C:\Program Files\Samsung\USB Driver for Mobile Phones",
                r"C:\Program Files (x86)\Samsung\USB Driver for Mobile Phones",
                r"C:\Samsung\USB Driver for Mobile Phones"
            ]
            
            for path in common_paths:
                if os.path.exists(path):
                    logger.info(f"Found Samsung drivers at: {path}")
                    return path
                    
            # Check registry for Samsung drivers
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                  r"SOFTWARE\Samsung\USB Driver for Mobile Phones") as key:
                    path = winreg.QueryValueEx(key, "InstallPath")[0]
                    if os.path.exists(path):
                        logger.info(f"Found Samsung drivers in registry: {path}")
                        return path
            except (FileNotFoundError, OSError):
                pass
                
            logger.warning("Samsung drivers not found")
            return None
            
        except Exception as e:
            logger.error(f"Error finding Samsung drivers: {str(e)}")
            return None
    
    def enable_debugging(self, serial):
        """Enable ADB debugging on a device with advanced methods."""
        try:
            # Method 1: Standard ADB enable
            success, message = self._standard_adb_enable(serial)
            if success:
                return True, message, False
                
            # Method 2: Samsung-specific enable using drivers
            if self.samsung_drivers_path:
                success, message = self._samsung_adb_enable(serial)
                if success:
                    return True, message, False
                    
            # Method 3: Force enable through system properties
            success, message = self._force_adb_enable(serial)
            if success:
                return True, message, True  # Requires confirmation
                
            return False, "All ADB enable methods failed", False
            
        except Exception as e:
            logger.error(f"Error enabling ADB debugging: {str(e)}")
            return False, f"Error: {str(e)}", False
    
    def _standard_adb_enable(self, serial):
        """Standard method to enable ADB debugging."""
        try:
            # Enable USB debugging through settings
            cmd = [self.adb_path, '-s', serial, 'shell', 'settings', 'put', 'global', 'adb_enabled', '1']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                return True, "ADB debugging enabled successfully"
            else:
                return False, f"Failed to enable ADB: {result.stderr}"
                
        except Exception as e:
            return False, f"Standard ADB enable failed: {str(e)}"
    
    def _samsung_adb_enable(self, serial):
        """Samsung-specific method using Samsung drivers."""
        try:
            if not self.samsung_drivers_path:
                return False, "Samsung drivers not available"
                
            # Use Samsung's proprietary tools if available
            samsung_tool = os.path.join(self.samsung_drivers_path, "SamsungAndroidDebugBridge.exe")
            if os.path.exists(samsung_tool):
                cmd = [samsung_tool, '-s', serial, 'enable-debug']
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
                
                if result.returncode == 0:
                    return True, "ADB enabled using Samsung drivers"
                    
            # Alternative: Use Samsung USB driver capabilities
            return self._samsung_driver_enable(serial)
            
        except Exception as e:
            return False, f"Samsung ADB enable failed: {str(e)}"
    
    def _samsung_driver_enable(self, serial):
        """Enable ADB using Samsung driver capabilities."""
        try:
            # This would interface with Samsung's USB driver
            # Implementation depends on Samsung driver API
            logger.info(f"Attempting Samsung driver ADB enable for {serial}")
            
            # Placeholder for Samsung driver integration
            # In a real implementation, this would use Samsung's driver API
            return False, "Samsung driver integration not implemented"
            
        except Exception as e:
            return False, f"Samsung driver enable failed: {str(e)}"
    
    def _force_adb_enable(self, serial):
        """Force enable ADB through system-level modifications."""
        try:
            # Enable through system properties
            commands = [
                ['shell', 'setprop', 'service.adb.tcp.port', '5555'],
                ['shell', 'setprop', 'persist.service.adb.enable', '1'],
                ['shell', 'setprop', 'persist.service.debuggable', '1'],
                ['shell', 'setprop', 'persist.sys.usb.config', 'mtp,adb']
            ]
            
            success_count = 0
            for cmd in commands:
                try:
                    full_cmd = [self.adb_path, '-s', serial] + cmd
                    result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        success_count += 1
                except Exception:
                    continue
                    
            if success_count > 0:
                return True, f"Force enabled ADB ({success_count}/{len(commands)} methods succeeded)"
            else:
                return False, "All force enable methods failed"
                
        except Exception as e:
            return False, f"Force ADB enable failed: {str(e)}"
    
    def disable_debugging(self, serial):
        """Disable ADB debugging on a device."""
        try:
            # Disable USB debugging
            cmd = [self.adb_path, '-s', serial, 'shell', 'settings', 'put', 'global', 'adb_enabled', '0']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                return True, "ADB debugging disabled successfully"
            else:
                return False, f"Failed to disable ADB: {result.stderr}"
                
        except Exception as e:
            return False, f"Error disabling ADB: {str(e)}"

class SamsungDeviceManager:
    """Samsung-specific device management with advanced features."""
    
    def __init__(self, adb_path='adb'):
        self.adb_path = adb_path
        self.samsung_drivers_path = self._find_samsung_drivers()
        
    def _find_samsung_drivers(self):
        """Find Samsung USB drivers."""
        # Same implementation as ADBManager
        return ADBManager(self.adb_path)._find_samsung_drivers()
    
    def activate_mtp_locked_device(self, serial):
        """Activate MTP on a locked Samsung device using driver capabilities."""
        try:
            logger.info(f"Attempting MTP activation on locked device {serial}")
            
            # Method 1: Use Samsung driver direct access
            success, message = self._samsung_driver_mtp_enable(serial)
            if success:
                return True, message
                
            # Method 2: Force MTP through USB configuration
            success, message = self._force_mtp_activation(serial)
            if success:
                return True, message
                
            # Method 3: Use Samsung proprietary commands
            success, message = self._samsung_proprietary_mtp(serial)
            if success:
                return True, message
                
            return False, "All MTP activation methods failed"
            
        except Exception as e:
            logger.error(f"Error activating MTP: {str(e)}")
            return False, f"MTP activation error: {str(e)}"
    
    def _samsung_driver_mtp_enable(self, serial):
        """Enable MTP using Samsung driver capabilities."""
        try:
            if not self.samsung_drivers_path:
                return False, "Samsung drivers not available"
                
            # This would interface with Samsung's USB driver to force MTP mode
            # even on locked devices - requires Samsung driver API access
            logger.info(f"Using Samsung drivers for MTP activation on {serial}")
            
            # Placeholder for Samsung driver MTP activation
            # Real implementation would use Samsung's driver API
            return False, "Samsung driver MTP activation not implemented"
            
        except Exception as e:
            return False, f"Samsung driver MTP failed: {str(e)}"
    
    def _force_mtp_activation(self, serial):
        """Force MTP activation through USB configuration."""
        try:
            # Force USB configuration to MTP mode
            commands = [
                ['shell', 'setprop', 'sys.usb.config', 'mtp'],
                ['shell', 'setprop', 'persist.sys.usb.config', 'mtp'],
                ['shell', 'svc', 'usb', 'setFunction', 'mtp']
            ]
            
            for cmd in commands:
                try:
                    full_cmd = [self.adb_path, '-s', serial] + cmd
                    result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        time.sleep(2)  # Wait for USB reconfiguration
                        return True, "MTP activated successfully"
                except Exception:
                    continue
                    
            return False, "Failed to force MTP activation"
            
        except Exception as e:
            return False, f"Force MTP activation failed: {str(e)}"
    
    def _samsung_proprietary_mtp(self, serial):
        """Use Samsung proprietary commands for MTP activation."""
        try:
            # Samsung-specific commands for MTP activation
            samsung_commands = [
                ['shell', 'am', 'broadcast', '-a', 'com.samsung.android.MTP_ENABLE'],
                ['shell', 'service', 'call', 'usb', '1'],  # Samsung USB service call
                ['shell', 'setprop', 'ro.sys.usb.mtp.whql.enable', '1']
            ]
            
            for cmd in samsung_commands:
                try:
                    full_cmd = [self.adb_path, '-s', serial] + cmd
                    result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        return True, "MTP activated using Samsung proprietary method"
                except Exception:
                    continue
                    
            return False, "Samsung proprietary MTP activation failed"
            
        except Exception as e:
            return False, f"Samsung proprietary MTP failed: {str(e)}"
    
    def bypass_frp(self, serial):
        """Attempt FRP (Factory Reset Protection) bypass using various methods."""
        try:
            logger.warning(f"Attempting FRP bypass on device {serial} - Use only on authorized devices!")
            
            # Method 1: Samsung-specific FRP bypass
            success, message = self._samsung_frp_bypass(serial)
            if success:
                return True, message
                
            # Method 2: Generic Android FRP bypass
            success, message = self._generic_frp_bypass(serial)
            if success:
                return True, message
                
            # Method 3: Advanced FRP bypass techniques
            success, message = self._advanced_frp_bypass(serial)
            if success:
                return True, message
                
            return False, "All FRP bypass methods failed"
            
        except Exception as e:
            logger.error(f"Error in FRP bypass: {str(e)}")
            return False, f"FRP bypass error: {str(e)}"
    
    def _samsung_frp_bypass(self, serial):
        """Samsung-specific FRP bypass methods."""
        try:
            # Samsung FRP bypass techniques
            bypass_commands = [
                # Remove FRP lock
                ['shell', 'rm', '-rf', '/data/system/users/0/settings_secure.xml'],
                ['shell', 'rm', '-rf', '/data/system/users/0/settings_global.xml'],
                # Disable FRP service
                ['shell', 'pm', 'disable-user', 'com.google.android.gms/.auth.setup.DeviceOwnerSetupService'],
                # Clear FRP data
                ['shell', 'rm', '-rf', '/persistent/frp']
            ]
            
            success_count = 0
            for cmd in bypass_commands:
                try:
                    full_cmd = [self.adb_path, '-s', serial] + cmd
                    result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=15)
                    if result.returncode == 0:
                        success_count += 1
                except Exception:
                    continue
                    
            if success_count > 0:
                return True, f"Samsung FRP bypass completed ({success_count} methods succeeded)"
            else:
                return False, "Samsung FRP bypass failed"
                
        except Exception as e:
            return False, f"Samsung FRP bypass error: {str(e)}"
    
    def _generic_frp_bypass(self, serial):
        """Generic Android FRP bypass methods."""
        try:
            # Generic FRP bypass techniques
            generic_commands = [
                ['shell', 'content', 'insert', '--uri', 'content://settings/secure', 
                 '--bind', 'name:s:user_setup_complete', '--bind', 'value:i:1'],
                ['shell', 'am', 'start', '-n', 'com.google.android.gsf.login/.LoginActivity'],
                ['shell', 'settings', 'put', 'secure', 'user_setup_complete', '1']
            ]
            
            for cmd in generic_commands:
                try:
                    full_cmd = [self.adb_path, '-s', serial] + cmd
                    result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=15)
                    if result.returncode == 0:
                        return True, "Generic FRP bypass successful"
                except Exception:
                    continue
                    
            return False, "Generic FRP bypass failed"
            
        except Exception as e:
            return False, f"Generic FRP bypass error: {str(e)}"
    
    def _advanced_frp_bypass(self, serial):
        """Advanced FRP bypass techniques."""
        try:
            # Advanced bypass methods
            advanced_commands = [
                # Modify build properties
                ['shell', 'setprop', 'ro.setupwizard.mode', 'DISABLED'],
                ['shell', 'setprop', 'ro.facelock.black_timeout', '0'],
                # Clear setup wizard data
                ['shell', 'pm', 'clear', 'com.google.android.setupwizard'],
                ['shell', 'pm', 'clear', 'com.android.provision']
            ]
            
            for cmd in advanced_commands:
                try:
                    full_cmd = [self.adb_path, '-s', serial] + cmd
                    result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=15)
                    if result.returncode == 0:
                        return True, "Advanced FRP bypass successful"
                except Exception:
                    continue
                    
            return False, "Advanced FRP bypass failed"
            
        except Exception as e:
            return False, f"Advanced FRP bypass error: {str(e)}"

    def manage_mdm(self, serial, action='disable'):
        """Manage MDM (Mobile Device Management) on Samsung devices."""
        try:
            logger.info(f"Managing MDM on device {serial}: {action}")

            if action == 'disable':
                return self._disable_mdm(serial)
            elif action == 'enable':
                return self._enable_mdm(serial)
            elif action == 'bypass':
                return self._bypass_mdm(serial)
            elif action == 'status':
                return self._get_mdm_status(serial)
            else:
                return False, f"Unknown MDM action: {action}"

        except Exception as e:
            logger.error(f"Error managing MDM: {str(e)}")
            return False, f"MDM management error: {str(e)}"

    def _disable_mdm(self, serial):
        """Disable MDM on the device."""
        try:
            # Common MDM packages to disable
            mdm_packages = [
                'com.samsung.android.knox.containercore',
                'com.samsung.android.knox.kpecore',
                'com.samsung.android.mdm',
                'com.samsung.klmsagent',
                'com.samsung.android.mdx',
                'com.android.managedprovisioning',
                'com.google.android.apps.work.oobconfig'
            ]

            disabled_count = 0
            for package in mdm_packages:
                try:
                    # Disable the MDM package
                    cmd = [self.adb_path, '-s', serial, 'shell', 'pm', 'disable-user', package]
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        disabled_count += 1
                        logger.info(f"Disabled MDM package: {package}")
                except Exception:
                    continue

            # Clear MDM data
            mdm_clear_commands = [
                ['shell', 'pm', 'clear', 'com.samsung.android.knox.containercore'],
                ['shell', 'rm', '-rf', '/data/system/enterprise'],
                ['shell', 'rm', '-rf', '/data/misc/knox'],
                ['shell', 'settings', 'put', 'secure', 'device_provisioned', '1']
            ]

            for cmd in mdm_clear_commands:
                try:
                    full_cmd = [self.adb_path, '-s', serial] + cmd
                    subprocess.run(full_cmd, capture_output=True, text=True, timeout=10)
                except Exception:
                    continue

            if disabled_count > 0:
                return True, f"MDM disabled successfully ({disabled_count} packages disabled)"
            else:
                return False, "Failed to disable MDM packages"

        except Exception as e:
            return False, f"MDM disable error: {str(e)}"

    def _enable_mdm(self, serial):
        """Enable MDM on the device."""
        try:
            # Re-enable MDM packages
            mdm_packages = [
                'com.samsung.android.knox.containercore',
                'com.samsung.android.knox.kpecore',
                'com.samsung.android.mdm',
                'com.samsung.klmsagent'
            ]

            enabled_count = 0
            for package in mdm_packages:
                try:
                    cmd = [self.adb_path, '-s', serial, 'shell', 'pm', 'enable', package]
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        enabled_count += 1
                except Exception:
                    continue

            if enabled_count > 0:
                return True, f"MDM enabled successfully ({enabled_count} packages enabled)"
            else:
                return False, "Failed to enable MDM packages"

        except Exception as e:
            return False, f"MDM enable error: {str(e)}"

    def _bypass_mdm(self, serial):
        """Bypass MDM restrictions."""
        try:
            # MDM bypass techniques
            bypass_commands = [
                # Remove device admin
                ['shell', 'dpm', 'remove-active-admin', 'com.samsung.android.knox.containercore/.admin.DeviceAdminReceiver'],
                # Clear device owner
                ['shell', 'dpm', 'clear-device-owner'],
                # Remove work profile
                ['shell', 'pm', 'remove-user', '10'],
                # Disable MDM enforcement
                ['shell', 'settings', 'put', 'global', 'device_owner_present', '0']
            ]

            success_count = 0
            for cmd in bypass_commands:
                try:
                    full_cmd = [self.adb_path, '-s', serial] + cmd
                    result = subprocess.run(full_cmd, capture_output=True, text=True, timeout=15)
                    if result.returncode == 0:
                        success_count += 1
                except Exception:
                    continue

            if success_count > 0:
                return True, f"MDM bypass completed ({success_count} methods succeeded)"
            else:
                return False, "MDM bypass failed"

        except Exception as e:
            return False, f"MDM bypass error: {str(e)}"

    def _get_mdm_status(self, serial):
        """Get MDM status on the device."""
        try:
            status = {
                'device_owner': None,
                'profile_owner': None,
                'knox_enabled': False,
                'mdm_packages': []
            }

            # Check device owner
            try:
                cmd = [self.adb_path, '-s', serial, 'shell', 'dpm', 'list-owners']
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0 and result.stdout.strip():
                    status['device_owner'] = result.stdout.strip()
            except Exception:
                pass

            # Check Knox status
            try:
                cmd = [self.adb_path, '-s', serial, 'shell', 'pm', 'list-packages', 'knox']
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0 and 'knox' in result.stdout:
                    status['knox_enabled'] = True
                    status['mdm_packages'] = [line.strip().replace('package:', '')
                                            for line in result.stdout.splitlines()
                                            if line.strip().startswith('package:')]
            except Exception:
                pass

            return True, status

        except Exception as e:
            return False, f"MDM status error: {str(e)}"

class AdvancedDeviceController:
    """Advanced device control features for comprehensive device management."""

    def __init__(self, adb_path='adb'):
        self.adb_path = adb_path
        self.screen_recording = False
        self.remote_control_active = False

    def remote_screen_control(self, serial, action='start'):
        """Enable remote screen control using scrcpy or similar tools."""
        try:
            if action == 'start':
                return self._start_screen_control(serial)
            elif action == 'stop':
                return self._stop_screen_control(serial)
            elif action == 'screenshot':
                return self._take_screenshot(serial)
            elif action == 'record':
                return self._start_screen_recording(serial)
            else:
                return False, f"Unknown screen control action: {action}"

        except Exception as e:
            return False, f"Screen control error: {str(e)}"

    def _start_screen_control(self, serial):
        """Start remote screen control session."""
        try:
            # Check if scrcpy is available
            scrcpy_path = self._find_scrcpy()
            if scrcpy_path:
                # Start scrcpy for screen mirroring and control
                cmd = [scrcpy_path, '-s', serial, '--stay-awake', '--turn-screen-off']
                subprocess.Popen(cmd, creationflags=subprocess.CREATE_NEW_CONSOLE)
                self.remote_control_active = True
                return True, "Remote screen control started with scrcpy"
            else:
                # Fallback to basic screen mirroring
                return self._basic_screen_mirror(serial)

        except Exception as e:
            return False, f"Screen control start error: {str(e)}"

    def _find_scrcpy(self):
        """Find scrcpy installation."""
        try:
            # Check if scrcpy is in PATH
            result = subprocess.run(['scrcpy', '--version'],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return 'scrcpy'
        except Exception:
            pass

        # Check common installation paths
        common_paths = [
            r"C:\Program Files\scrcpy\scrcpy.exe",
            r"C:\Program Files (x86)\scrcpy\scrcpy.exe",
            r"C:\scrcpy\scrcpy.exe"
        ]

        for path in common_paths:
            if os.path.exists(path):
                return path

        return None

    def _basic_screen_mirror(self, serial):
        """Basic screen mirroring without scrcpy."""
        try:
            # Enable screen mirroring through ADB
            cmd = [self.adb_path, '-s', serial, 'shell', 'settings', 'put', 'global', 'stay_on_while_plugged_in', '7']
            subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            return True, "Basic screen mirroring enabled (install scrcpy for full control)"

        except Exception as e:
            return False, f"Basic screen mirror error: {str(e)}"
