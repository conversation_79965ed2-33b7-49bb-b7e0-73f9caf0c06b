'use strict';

/* jshint quotmark: double */
window.SwaggerTranslator.learn({
    "Warning: Deprecated":"Avertissement : Obsolète",
    "Implementation Notes":"Notes d'implémentation",
    "Response Class":"Classe de la réponse",
    "Status":"Statut",
    "Parameters":"Paramètres",
    "Parameter":"Paramètre",
    "Value":"Valeur",
    "Description":"Description",
    "Parameter Type":"Type du paramètre",
    "Data Type":"Type de données",
    "Response Messages":"Messages de la réponse",
    "HTTP Status Code":"Code de statut HTTP",
    "Reason":"Raison",
    "Response Model":"Modèle de réponse",
    "Request URL":"URL appelée",
    "Response Body":"Corps de la réponse",
    "Response Code":"Code de la réponse",
    "Response Headers":"En-têtes de la réponse",
    "Hide Response":"Cacher la réponse",
    "Headers":"En-têtes",
    "Try it out!":"Testez !",
    "Show/Hide":"Afficher/Masquer",
    "List Operations":"Liste des opérations",
    "Expand Operations":"Développer les opérations",
    "Raw":"Brut",
    "can't parse JSON.  Raw result":"impossible de décoder le JSON.  Résultat brut",
    "Example Value":"Exemple la valeur",
    "Model Schema":"Définition du modèle",
    "Model":"Modèle",
    "apply":"appliquer",
    "Username":"Nom d'utilisateur",
    "Password":"Mot de passe",
    "Terms of service":"Conditions de service",
    "Created by":"Créé par",
    "See more at":"Voir plus sur",
    "Contact the developer":"Contacter le développeur",
    "api version":"version de l'api",
    "Response Content Type":"Content Type de la réponse",
    "fetching resource":"récupération de la ressource",
    "fetching resource list":"récupération de la liste de ressources",
    "Explore":"Explorer",
    "Show Swagger Petstore Example Apis":"Montrer les Apis de l'exemple Petstore de Swagger",
    "Can't read from server.  It may not have the appropriate access-control-origin settings.":"Impossible de lire à partir du serveur. Il se peut que les réglages access-control-origin ne soient pas appropriés.",
    "Please specify the protocol for":"Veuillez spécifier un protocole pour",
    "Can't read swagger JSON from":"Impossible de lire le JSON swagger à partir de",
    "Finished Loading Resource Information. Rendering Swagger UI":"Chargement des informations terminé. Affichage de Swagger UI",
    "Unable to read api":"Impossible de lire l'api",
    "from path":"à partir du chemin",
    "server returned":"réponse du serveur"
});
