class DebugManager {
    constructor() {
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Delegate events for debug controls
        document.addEventListener('click', (e) => {
            if (e.target.matches('.enable-debugging')) {
                const deviceCard = e.target.closest('.device-card');
                if (deviceCard) {
                    const serial = deviceCard.querySelector('.device-serial').textContent;
                    this.toggleDebugging(serial, true);
                }
            } else if (e.target.matches('.disable-debugging')) {
                const deviceCard = e.target.closest('.device-card');
                if (deviceCard) {
                    const serial = deviceCard.querySelector('.device-serial').textContent;
                    this.toggleDebugging(serial, false);
                }
            } else if (e.target.matches('.check-debug-status')) {
                const deviceCard = e.target.closest('.device-card');
                if (deviceCard) {
                    const serial = deviceCard.querySelector('.device-serial').textContent;
                    this.checkDebugStatus(serial);
                }
            }
        });
    }

    async toggleDebugging(serial, enable) {
        try {
            const endpoint = enable ? 'enable' : 'disable';
            const response = await fetch(`/api/devices/${serial}/debug/${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `Failed to ${enable ? 'enable' : 'disable'} debugging`);
            }

            if (data.requires_confirmation) {
                this.showConfirmationAlert(serial);
            } else {
                this.updateDebugStatus(serial, data.status);
                this.showSuccess(`USB Debugging ${enable ? 'enabled' : 'disabled'} successfully`);
            }
        } catch (error) {
            this.showError('Error', error.message);
        }
    }

    async checkDebugStatus(serial) {
        try {
            const response = await fetch(`/api/devices/${serial}/debug/status`);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to get debug status');
            }

            this.showDebugStatusModal(serial, data.status);
        } catch (error) {
            this.showError('Error', error.message);
        }
    }

    updateDebugStatus(serial, status) {
        const deviceCard = document.querySelector(`.device-card[data-serial="${serial}"]`);
        if (!deviceCard) return;

        const debugButton = deviceCard.querySelector('.enable-debugging');
        if (debugButton) {
            if (status.debugging_enabled) {
                debugButton.classList.remove('btn-success');
                debugButton.classList.add('btn-danger');
                debugButton.innerHTML = '<i class="fas fa-bug"></i> Disable Debugging';
                debugButton.classList.replace('enable-debugging', 'disable-debugging');
            } else {
                debugButton.classList.remove('btn-danger');
                debugButton.classList.add('btn-success');
                debugButton.innerHTML = '<i class="fas fa-bug"></i> Enable Debugging';
                debugButton.classList.replace('disable-debugging', 'enable-debugging');
            }
        }

        // Update status indicators
        const statusIndicators = deviceCard.querySelector('.debug-status-indicators');
        if (statusIndicators) {
            statusIndicators.innerHTML = this.getStatusIndicatorsHtml(status);
        }
    }

    getStatusIndicatorsHtml(status) {
        return `
            <div class="mt-2">
                <span class="badge ${status.debugging_enabled ? 'bg-success' : 'bg-danger'} me-1">
                    <i class="fas fa-bug"></i> Debug
                </span>
                <span class="badge ${status.device_authorized ? 'bg-success' : 'bg-warning'} me-1">
                    <i class="fas fa-key"></i> Auth
                </span>
                <span class="badge ${status.developer_options ? 'bg-success' : 'bg-secondary'} me-1">
                    <i class="fas fa-code"></i> Dev
                </span>
            </div>
        `;
    }

    showDebugStatusModal(serial, status) {
        let errorList = '';
        if (status.error_details && status.error_details.length > 0) {
            errorList = `
                <div class="alert alert-warning mt-3">
                    <h6>Issues to Resolve:</h6>
                    <ul class="mb-0">
                        ${status.error_details.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        const modalContent = `
            <div class="table-responsive">
                <table class="table">
                    <tbody>
                        <tr>
                            <th>USB Debugging</th>
                            <td>
                                <span class="badge ${status.debugging_enabled ? 'bg-success' : 'bg-danger'}">
                                    ${status.debugging_enabled ? 'Enabled' : 'Disabled'}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>Device Authorization</th>
                            <td>
                                <span class="badge ${status.device_authorized ? 'bg-success' : 'bg-warning'}">
                                    ${status.device_authorized ? 'Authorized' : 'Unauthorized'}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>Developer Options</th>
                            <td>
                                <span class="badge ${status.developer_options ? 'bg-success' : 'bg-secondary'}">
                                    ${status.developer_options ? 'Enabled' : 'Disabled'}
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
                ${errorList}
            </div>
        `;

        Swal.fire({
            title: 'USB Debugging Status',
            html: modalContent,
            width: '600px',
            showConfirmButton: true,
            confirmButtonText: 'Close'
        });
    }

    showConfirmationAlert(serial) {
        Swal.fire({
            title: 'USB Debugging Authorization Required',
            html: `
                <div class="alert alert-info">
                    <p><strong>Please check your device:</strong></p>
                    <p>A USB debugging authorization dialog should appear on your device.</p>
                    <p>To enable USB debugging:</p>
                    <ol>
                        <li>Look for the authorization dialog on your device screen</li>
                        <li>Check "Always allow from this computer" (recommended)</li>
                        <li>Tap "Allow" to authorize USB debugging</li>
                    </ol>
                </div>
            `,
            icon: 'info',
            showCancelButton: true,
            confirmButtonText: 'Check Status',
            cancelButtonText: 'Close',
            showCloseButton: true
        }).then((result) => {
            if (result.isConfirmed) {
                this.checkDebugStatus(serial);
            }
        });
    }

    showError(title, message) {
        Swal.fire({
            title: title,
            text: message,
            icon: 'error',
            confirmButtonText: 'OK'
        });
    }

    showSuccess(message) {
        Swal.fire({
            title: 'Success',
            text: message,
            icon: 'success',
            confirmButtonText: 'OK',
            timer: 2000,
            timerProgressBar: true
        });
    }
}

// Initialize debug manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.debugManager = new DebugManager();
});