from flask import Flask, request, jsonify, render_template_string, send_from_directory, redirect
import os
import subprocess
import shlex
import re
import json
import logging
from datetime import datetime
from functools import wraps
from werkzeug.utils import secure_filename
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_httpauth import HTTPBasicAuth
from werkzeug.security import generate_password_hash, check_password_hash
from flasgger import Swagger, swag_from
from usb_manager import get_usb_devices, USBDeviceManager
from advanced_device_manager import ADBManager, SamsungDeviceManager, AdvancedDeviceController

# Simple Flask app to receive APK uploads and install via ADB on authorized devices
# IMPORTANT: This assumes devices are unlocked for ADB (USB debugging enabled and authorized)

app = Flask(__name__)

# Configure Swagger documentation
swagger_config = {
    "headers": [],
    "specs": [
        {
            "endpoint": "apispec",
            "route": "/apispec.json",
            "rule_filter": lambda rule: True,
            "model_filter": lambda tag: True,
        }
    ],
    "static_url_path": "/flasgger_static",
    "swagger_ui": True,
    "specs_route": "/docs",
    "title": "Android Device Manager API",
    "description": "API for managing Android devices and APK installations",
    "version": "1.0.0",
    "termsOfService": "",
    "contact": {
        "email": "<EMAIL>"
    },
    "license": {
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT"
    },
    "schemes": ["http", "https"]
}

swagger = Swagger(app, config=swagger_config)

# Configuration
UPLOAD_DIR = os.path.join(os.path.dirname(__file__), 'uploads')
STATIC_DIR = os.path.join(os.path.dirname(__file__), 'static')
TEMPLATES_DIR = os.path.join(os.path.dirname(__file__), 'templates')
ALLOWED_EXTENSIONS = {'.apk'}
ADB_PATH = os.environ.get('ADB_PATH', 'adb')  # Set to full path if not on PATH

# Create required directories
os.makedirs(STATIC_DIR, exist_ok=True)
os.makedirs(TEMPLATES_DIR, exist_ok=True)

# Setup rate limiting
limiter = Limiter(
    app=app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

# Setup authentication
auth = HTTPBasicAuth()
users = {
    "admin": generate_password_hash(os.environ.get('ADMIN_PASSWORD', 'admin'))
}

@auth.verify_password
def verify_password(username, password):
    if username in users and check_password_hash(users.get(username), password):
        return username

os.makedirs(UPLOAD_DIR, exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('adb_manager.log', mode='a')
    ]
)
logger = logging.getLogger(__name__)

# Log startup information
logger.info("Starting Android ADB Manager")
logger.info(f"ADB Path: {os.environ.get('ADB_PATH', 'adb (from PATH)')}")
logger.info(f"Upload Directory: {UPLOAD_DIR}")

# Initialize USB device manager
try:
    usb_manager = USBDeviceManager()
    logger.info("USB Device Manager initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize USB Device Manager: {str(e)}")
    usb_manager = None


def handle_adb_errors(f):
    """Decorator to handle common ADB errors and provide consistent error responses."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except subprocess.TimeoutExpired:
            logger.error("ADB command timed out")
            return jsonify(error="ADB command timed out. Device may be unresponsive."), 504
        except FileNotFoundError:
            logger.error("ADB not found")
            return jsonify(error="ADB not found. Please install Android SDK platform-tools."), 500
        except Exception as e:
            logger.error(f"Unexpected error in {f.__name__}: {str(e)}")
            return jsonify(error=f"Internal server error: {str(e)}"), 500
    return decorated_function


def validate_device_serial(serial):
    """Validate device serial to prevent command injection."""
    if not serial:
        return False
    # Device serials should be alphanumeric with possible hyphens, dots, and colons
    pattern = r'^[a-zA-Z0-9\-\.:_]+$'
    return bool(re.match(pattern, serial)) and len(serial) <= 100


def sanitize_input(input_str, max_length=255):
    """Sanitize input string to prevent injection attacks."""
    if not input_str:
        return ""
    # Remove any potentially dangerous characters
    sanitized = re.sub(r'[;&|`$(){}[\]<>]', '', str(input_str))
    return sanitized[:max_length]


def allowed_file(filename: str):
    _, ext = os.path.splitext(filename.lower())
    return ext in ALLOWED_EXTENSIONS


def run_adb_command(args, timeout=120):
    """Run an adb command and return (returncode, stdout, stderr)."""
    try:
        cmd = [ADB_PATH] + args
        logger.info(f"Executing ADB command: {' '.join(cmd)}")
        # Use text mode for easier parsing
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=timeout,
            shell=False,
        )
        logger.info(f"ADB command completed with return code: {result.returncode}")
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except FileNotFoundError:
        logger.error("ADB not found in PATH")
        return 127, '', 'adb not found. Set ADB_PATH env var or install platform-tools on PATH.'
    except subprocess.TimeoutExpired:
        logger.error(f"ADB command timed out after {timeout} seconds")
        return 124, '', 'adb command timed out.'


def validate_package_name(package_name):
    """Validate Android package name to prevent command injection."""
    if not package_name:
        return False
    # Sanitize input first
    package_name = sanitize_input(package_name)
    # Android package names should match this pattern
    pattern = r'^[a-zA-Z][a-zA-Z0-9_]*(\.[a-zA-Z][a-zA-Z0-9_]*)*$'
    return bool(re.match(pattern, package_name)) and len(package_name) <= 255


def validate_pid(pid):
    """Validate process ID to prevent command injection."""
    try:
        # Sanitize input first
        pid_str = sanitize_input(str(pid))
        pid_int = int(pid_str)
        return 1 <= pid_int <= 65535
    except (ValueError, TypeError):
        return False


def validate_and_get_device(serial=None):
    """Validate device serial and return device info, or auto-select if only one device."""
    if serial:
        serial = sanitize_input(serial)
        if not validate_device_serial(serial):
            return None, "Invalid device serial format"

    # Get connected devices
    devices, err = get_connected_devices()
    if err:
        return None, f"Failed to get devices: {err}"

    device_list = [d for d in devices if d['state'] == 'device']
    if len(device_list) == 0:
        return None, "No connected authorized devices"

    if not serial:
        if len(device_list) > 1:
            return None, "Multiple devices connected. Specify serial parameter."
        serial = device_list[0]['serial']
    else:
        # Verify the specified serial exists
        if not any(d['serial'] == serial for d in device_list):
            return None, f"Device {serial} not found or not authorized"

    return serial, None


def get_comprehensive_device_info(serial):
    """Get comprehensive device information including hardware, software, and system details."""
    if not validate_device_serial(serial):
        return None, "Invalid device serial"

    device_info = {'serial': serial, 'timestamp': datetime.now().isoformat()}

    # Basic device properties
    properties = {
        'ro.build.version.release': 'android_version',
        'ro.build.version.sdk': 'api_level',
        'ro.product.model': 'model',
        'ro.product.manufacturer': 'manufacturer',
        'ro.product.brand': 'brand',
        'ro.product.device': 'device_name',
        'ro.build.version.incremental': 'build_number',
        'ro.build.display.id': 'build_id',
        'ro.build.fingerprint': 'build_fingerprint',
        'ro.hardware': 'hardware',
        'ro.board.platform': 'platform',
        'ro.product.cpu.abi': 'cpu_abi',
        'ro.product.cpu.abilist': 'cpu_abi_list',
        'ro.build.characteristics': 'characteristics',
        'ro.debuggable': 'debuggable',
        'ro.secure': 'secure_boot',
        'ro.adb.secure': 'adb_secure'
    }

    logger.info(f"Gathering comprehensive device info for {serial}")

    for prop, key in properties.items():
        code, out, _ = run_adb_command(['-s', serial, 'shell', 'getprop', prop], timeout=10)
        if code == 0 and out.strip():
            device_info[key] = out.strip()

    return device_info, None


def get_device_storage_info(serial):
    """Get device storage information."""
    if not validate_device_serial(serial):
        return None, "Invalid device serial"

    storage_info = {}

    # Get storage usage with df command
    code, out, _ = run_adb_command(['-s', serial, 'shell', 'df', '/data'], timeout=15)
    if code == 0:
        lines = out.strip().splitlines()
        if len(lines) > 1:
            # Parse df output (format varies by Android version)
            data_line = lines[1].split()
            if len(data_line) >= 4:
                try:
                    storage_info['internal_total_kb'] = int(data_line[1])
                    storage_info['internal_used_kb'] = int(data_line[2])
                    storage_info['internal_available_kb'] = int(data_line[3])
                    storage_info['internal_usage_percent'] = round(
                        (storage_info['internal_used_kb'] / storage_info['internal_total_kb']) * 100, 2
                    )
                except (ValueError, ZeroDivisionError):
                    pass

    # Get external storage info if available
    code, out, _ = run_adb_command(['-s', serial, 'shell', 'df', '/sdcard'], timeout=15)
    if code == 0 and '/sdcard' in out:
        lines = out.strip().splitlines()
        if len(lines) > 1:
            data_line = lines[1].split()
            if len(data_line) >= 4:
                try:
                    storage_info['external_total_kb'] = int(data_line[1])
                    storage_info['external_used_kb'] = int(data_line[2])
                    storage_info['external_available_kb'] = int(data_line[3])
                except ValueError:
                    pass

    return storage_info, None


def get_installed_packages(serial, include_system=False):
    """Get list of installed packages on the device."""
    if not validate_device_serial(serial):
        return None, "Invalid device serial"

    # Get all packages
    cmd = ['shell', 'pm', 'list', 'packages']
    if not include_system:
        cmd.append('-3')  # Third-party packages only

    code, out, _ = run_adb_command(['-s', serial] + cmd, timeout=30)
    if code != 0:
        return None, "Failed to get package list"

    packages = []
    for line in out.splitlines():
        if line.startswith('package:'):
            package_name = line.replace('package:', '').strip()
            if package_name:
                packages.append(package_name)

    # Get additional info for each package (limited to first 50 to avoid timeout)
    detailed_packages = []
    for package in packages[:50]:
        package_info = {'name': package}

        # Get package version
        code, out, _ = run_adb_command([
            '-s', serial, 'shell', 'dumpsys', 'package', package
        ], timeout=10)

        if code == 0:
            for line in out.splitlines():
                if 'versionName=' in line:
                    version = line.split('versionName=')[1].split()[0]
                    package_info['version'] = version
                    break

        detailed_packages.append(package_info)

    return detailed_packages, None


def get_device_network_info(serial):
    """Get device network configuration and status."""
    if not validate_device_serial(serial):
        return None, "Invalid device serial"

    network_info = {}

    # Get WiFi status
    code, out, _ = run_adb_command(['-s', serial, 'shell', 'dumpsys', 'wifi'], timeout=15)
    if code == 0:
        for line in out.splitlines():
            if 'mWifiEnabled' in line:
                network_info['wifi_enabled'] = 'true' in line.lower()
            elif 'mNetworkInfo' in line and 'CONNECTED' in line:
                network_info['wifi_connected'] = True

    # Get IP configuration
    code, out, _ = run_adb_command(['-s', serial, 'shell', 'ip', 'addr', 'show', 'wlan0'], timeout=10)
    if code == 0:
        for line in out.splitlines():
            if 'inet ' in line and not line.strip().startswith('inet 127'):
                parts = line.strip().split()
                for part in parts:
                    if '/' in part and not part.startswith('127'):
                        network_info['ip_address'] = part.split('/')[0]
                        break

    # Get mobile data status
    code, out, _ = run_adb_command(['-s', serial, 'shell', 'dumpsys', 'telephony.registry'], timeout=15)
    if code == 0:
        for line in out.splitlines():
            if 'mDataConnectionState' in line:
                network_info['mobile_data_connected'] = 'CONNECTED' in line
                break

    return network_info, None


def download_apk_from_device(serial, package_name, output_path=None):
    """Download APK file from device."""
    if not validate_device_serial(serial) or not validate_package_name(package_name):
        return None, "Invalid serial or package name"

    # Get the APK path on device
    code, out, _ = run_adb_command([
        '-s', serial, 'shell', 'pm', 'path', package_name
    ], timeout=15)

    if code != 0 or not out.strip():
        return None, f"Package {package_name} not found on device"

    # Extract APK path (format: "package:/path/to/app.apk")
    apk_path = None
    for line in out.splitlines():
        if line.startswith('package:'):
            apk_path = line.replace('package:', '').strip()
            break

    if not apk_path:
        return None, "Could not determine APK path"

    # Set output path if not provided
    if not output_path:
        output_path = os.path.join(UPLOAD_DIR, f"{package_name}.apk")

    # Pull the APK from device
    logger.info(f"Downloading APK {package_name} from {serial} to {output_path}")
    code, out, err = run_adb_command([
        '-s', serial, 'pull', apk_path, output_path
    ], timeout=60)

    if code != 0:
        return None, f"Failed to download APK: {err}"

    # Verify file was downloaded
    if os.path.exists(output_path):
        file_size = os.path.getsize(output_path)
        return {
            'package_name': package_name,
            'local_path': output_path,
            'device_path': apk_path,
            'file_size': file_size
        }, None
    else:
        return None, "APK download failed - file not found"


def install_multiple_apks(serial, apk_paths, grant_permissions=True, replace=True):
    """Install multiple APK files to a device."""
    if not validate_device_serial(serial):
        return None, "Invalid device serial"

    results = []

    for apk_path in apk_paths:
        if not os.path.exists(apk_path):
            results.append({
                'apk_path': apk_path,
                'success': False,
                'error': 'File not found'
            })
            continue

        # Push APK to device
        filename = os.path.basename(apk_path)
        remote_path = f"/data/local/tmp/{filename}"

        code, out, err = run_adb_command(['-s', serial, 'push', apk_path, remote_path], timeout=120)
        if code != 0:
            results.append({
                'apk_path': apk_path,
                'success': False,
                'error': f'Push failed: {err}'
            })
            continue

        # Install APK
        pm_args = ['shell', 'pm', 'install']
        if replace:
            pm_args.append('-r')
        if grant_permissions:
            pm_args.append('-g')
        pm_args.append(remote_path)

        code, out, err = run_adb_command(['-s', serial] + pm_args, timeout=300)

        # Clean up temp file
        run_adb_command(['-s', serial, 'shell', 'rm', '-f', remote_path])

        success = code == 0 and ('Success' in out or 'Success' in err)
        results.append({
            'apk_path': apk_path,
            'success': success,
            'output': out if success else err,
            'error': None if success else f'Install failed: {err}'
        })

    return results, None


def uninstall_package(serial, package_name, keep_data=False):
    """Uninstall a package from the device."""
    if not validate_device_serial(serial) or not validate_package_name(package_name):
        return False, "Invalid serial or package name"

    cmd = ['shell', 'pm', 'uninstall']
    if keep_data:
        cmd.append('-k')  # Keep data and cache directories
    cmd.append(package_name)

    logger.info(f"Uninstalling package {package_name} from device {serial}")
    code, out, err = run_adb_command(['-s', serial] + cmd, timeout=60)

    if code == 0 and 'Success' in out:
        logger.info(f"Successfully uninstalled {package_name}")
        return True, "Package uninstalled successfully"
    else:
        logger.error(f"Failed to uninstall {package_name}: {err}")
        return False, f"Uninstall failed: {err}"


def get_package_detailed_info(serial, package_name):
    """Get detailed information about a specific package."""
    if not validate_device_serial(serial) or not validate_package_name(package_name):
        return None, "Invalid serial or package name"

    # Get package info using dumpsys
    code, out, _ = run_adb_command([
        '-s', serial, 'shell', 'dumpsys', 'package', package_name
    ], timeout=30)

    if code != 0:
        return None, "Package not found or dumpsys failed"

    package_info = {'package_name': package_name}

    # Parse dumpsys output
    for line in out.splitlines():
        line = line.strip()

        if 'versionName=' in line:
            package_info['version_name'] = line.split('versionName=')[1].split()[0]
        elif 'versionCode=' in line:
            try:
                package_info['version_code'] = int(line.split('versionCode=')[1].split()[0])
            except ValueError:
                pass
        elif 'firstInstallTime=' in line:
            package_info['first_install_time'] = line.split('firstInstallTime=')[1].strip()
        elif 'lastUpdateTime=' in line:
            package_info['last_update_time'] = line.split('lastUpdateTime=')[1].strip()
        elif 'targetSdk=' in line:
            try:
                package_info['target_sdk'] = int(line.split('targetSdk=')[1].split()[0])
            except ValueError:
                pass
        elif 'minSdk=' in line:
            try:
                package_info['min_sdk'] = int(line.split('minSdk=')[1].split()[0])
            except ValueError:
                pass
        elif 'dataDir=' in line:
            package_info['data_dir'] = line.split('dataDir=')[1].strip()
        elif 'codePath=' in line:
            package_info['code_path'] = line.split('codePath=')[1].strip()

    # Get permissions
    permissions = []
    in_permissions = False
    for line in out.splitlines():
        line = line.strip()
        if 'requested permissions:' in line:
            in_permissions = True
            continue
        elif in_permissions:
            if line.startswith('android.permission.') or line.startswith('com.'):
                permissions.append(line)
            elif not line or line.startswith('install permissions:'):
                break

    package_info['permissions'] = permissions

    return package_info, None


def enable_wireless_adb(serial, port=5555):
    """Enable wireless ADB on a device."""
    if not validate_device_serial(serial):
        return False, "Invalid device serial"

    if not (1024 <= port <= 65535):
        return False, "Invalid port number"

    logger.info(f"Enabling wireless ADB on device {serial} port {port}")

    # Enable TCP/IP mode on the device
    code, out, err = run_adb_command(['-s', serial, 'tcpip', str(port)], timeout=15)

    if code == 0:
        logger.info(f"Wireless ADB enabled on {serial}:{port}")
        return True, f"Wireless ADB enabled on port {port}. Device will restart ADB in TCP mode."
    else:
        logger.error(f"Failed to enable wireless ADB: {err}")
        return False, f"Failed to enable wireless ADB: {err}"


def connect_wireless_device(ip_address, port=5555):
    """Connect to a device over WiFi."""
    if not ip_address:
        return False, "IP address is required"

    # Basic IP validation
    ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
    if not re.match(ip_pattern, ip_address):
        return False, "Invalid IP address format"

    if not (1024 <= port <= 65535):
        return False, "Invalid port number"

    target = f"{ip_address}:{port}"
    logger.info(f"Connecting to wireless device at {target}")

    code, out, err = run_adb_command(['connect', target], timeout=20)

    if code == 0:
        if 'connected' in out.lower():
            logger.info(f"Successfully connected to {target}")
            return True, f"Connected to {target}"
        elif 'already connected' in out.lower():
            logger.info(f"Already connected to {target}")
            return True, f"Already connected to {target}"
        else:
            logger.warning(f"Unexpected response: {out}")
            return False, f"Unexpected response: {out}"
    else:
        logger.error(f"Failed to connect to {target}: {err}")
        return False, f"Connection failed: {err}"


def disconnect_wireless_device(ip_address, port=5555):
    """Disconnect from a wireless device."""
    if not ip_address:
        return False, "IP address is required"

    target = f"{ip_address}:{port}"
    logger.info(f"Disconnecting from wireless device at {target}")

    code, out, err = run_adb_command(['disconnect', target], timeout=10)

    if code == 0:
        logger.info(f"Disconnected from {target}")
        return True, f"Disconnected from {target}"
    else:
        logger.error(f"Failed to disconnect from {target}: {err}")
        return False, f"Disconnect failed: {err}"


def pair_wireless_device(ip_address, port, pairing_code):
    """Pair with a device using wireless debugging (Android 11+)."""
    if not ip_address or not pairing_code:
        return False, "IP address and pairing code are required"

    # Sanitize pairing code (should be 6 digits)
    pairing_code = sanitize_input(pairing_code)
    if not pairing_code.isdigit() or len(pairing_code) != 6:
        return False, "Pairing code must be 6 digits"

    if not (1024 <= port <= 65535):
        return False, "Invalid port number"

    target = f"{ip_address}:{port}"
    logger.info(f"Pairing with wireless device at {target}")

    code, out, err = run_adb_command(['pair', target, pairing_code], timeout=30)

    if code == 0:
        if 'successfully paired' in out.lower():
            logger.info(f"Successfully paired with {target}")
            return True, f"Successfully paired with {target}"
        else:
            logger.warning(f"Unexpected pairing response: {out}")
            return False, f"Pairing response: {out}"
    else:
        logger.error(f"Failed to pair with {target}: {err}")
        return False, f"Pairing failed: {err}"


def get_device_ip_address(serial):
    """Get the IP address of a connected device."""
    if not validate_device_serial(serial):
        return None, "Invalid device serial"

    # Try to get IP from wlan0 interface
    code, out, _ = run_adb_command([
        '-s', serial, 'shell', 'ip', 'route', 'show', 'dev', 'wlan0'
    ], timeout=10)

    if code == 0:
        for line in out.splitlines():
            if 'src' in line:
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == 'src' and i + 1 < len(parts):
                        ip = parts[i + 1]
                        # Validate IP format
                        ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
                        if re.match(ip_pattern, ip):
                            return ip, None

    # Fallback: try alternative method
    code, out, _ = run_adb_command([
        '-s', serial, 'shell', 'ifconfig', 'wlan0'
    ], timeout=10)

    if code == 0:
        for line in out.splitlines():
            if 'inet addr:' in line:
                ip = line.split('inet addr:')[1].split()[0]
                return ip, None
            elif 'inet ' in line and not line.strip().startswith('inet 127'):
                parts = line.strip().split()
                for part in parts:
                    if '.' in part and not part.startswith('127'):
                        ip = part.split('/')[0] if '/' in part else part
                        ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
                        if re.match(ip_pattern, ip):
                            return ip, None

    return None, "Could not determine device IP address"


def get_sim_info(serial):
    """Get SIM card information from the device."""
    if not validate_device_serial(serial):
        return None, "Invalid device serial"

    sim_info = {}

    # Get SIM count
    code, out, _ = run_adb_command([
        '-s', serial, 'shell', 'getprop', 'ro.telephony.call_ring.multiple'
    ], timeout=10)

    # Get telephony info
    code, out, _ = run_adb_command([
        '-s', serial, 'shell', 'dumpsys', 'telephony.registry'
    ], timeout=15)

    if code == 0:
        sim_states = []
        for line in out.splitlines():
            if 'mSimState' in line:
                sim_states.append(line.strip())
        sim_info['sim_states'] = sim_states

    # Get SIM operator info
    for slot in range(2):  # Check for dual SIM
        code, out, _ = run_adb_command([
            '-s', serial, 'shell', 'getprop', f'gsm.sim.operator.alpha.{slot}'
        ], timeout=10)
        if code == 0 and out.strip():
            sim_info[f'sim_{slot}_operator'] = out.strip()

        code, out, _ = run_adb_command([
            '-s', serial, 'shell', 'getprop', f'gsm.sim.state.{slot}'
        ], timeout=10)
        if code == 0 and out.strip():
            sim_info[f'sim_{slot}_state'] = out.strip()

    return sim_info, None


def toggle_sim_card(serial, sim_slot, enable=True):
    """Enable or disable a SIM card slot."""
    if not validate_device_serial(serial):
        return False, "Invalid device serial"

    if sim_slot not in [0, 1]:
        return False, "SIM slot must be 0 or 1"

    action = "enable" if enable else "disable"
    logger.info(f"Attempting to {action} SIM slot {sim_slot} on device {serial}")

    # Try different methods for SIM control
    methods = [
        # Method 1: Using telephony service
        ['shell', 'service', 'call', 'phone', '134', 'i32', str(sim_slot), 'i32', '1' if enable else '0'],
        # Method 2: Using settings
        ['shell', 'settings', 'put', 'global', f'multi_sim_data_call_subscription_{sim_slot}', '1' if enable else '0'],
        # Method 3: Using telephony manager
        ['shell', 'am', 'broadcast', '-a', 'android.intent.action.SIM_STATE_CHANGED',
         '--ei', 'slot', str(sim_slot), '--es', 'state', 'LOADED' if enable else 'ABSENT']
    ]

    for method in methods:
        code, out, err = run_adb_command(['-s', serial] + method, timeout=15)
        if code == 0:
            logger.info(f"Successfully {action}d SIM slot {sim_slot} using method")
            return True, f"SIM slot {sim_slot} {action}d successfully"

    # Fallback: Try to disable mobile data for the SIM
    if not enable:
        code, out, err = run_adb_command([
            '-s', serial, 'shell', 'svc', 'data', 'disable'
        ], timeout=10)
        if code == 0:
            return True, f"Mobile data disabled for SIM slot {sim_slot}"

    return False, f"Failed to {action} SIM slot {sim_slot}. This may require root access or specific device support."


def set_preferred_sim(serial, sim_slot, service_type='data'):
    """Set preferred SIM for data, calls, or SMS."""
    if not validate_device_serial(serial):
        return False, "Invalid device serial"

    if sim_slot not in [0, 1]:
        return False, "SIM slot must be 0 or 1"

    if service_type not in ['data', 'voice', 'sms']:
        return False, "Service type must be 'data', 'voice', or 'sms'"

    logger.info(f"Setting preferred SIM for {service_type} to slot {sim_slot} on device {serial}")

    # Map service types to settings
    setting_map = {
        'data': 'multi_sim_data_call',
        'voice': 'multi_sim_voice_call',
        'sms': 'multi_sim_sms'
    }

    setting_name = setting_map[service_type]

    code, out, err = run_adb_command([
        '-s', serial, 'shell', 'settings', 'put', 'global', setting_name, str(sim_slot)
    ], timeout=15)

    if code == 0:
        logger.info(f"Successfully set preferred SIM for {service_type} to slot {sim_slot}")
        return True, f"Preferred SIM for {service_type} set to slot {sim_slot}"
    else:
        logger.error(f"Failed to set preferred SIM: {err}")
        return False, f"Failed to set preferred SIM: {err}"


def execute_shell_command(serial, command, timeout=30):
    """Execute a shell command on the device with security restrictions."""
    if not validate_device_serial(serial):
        return None, "Invalid device serial"

    # Security: Sanitize and validate command
    command = sanitize_input(command, max_length=500)

    # Blacklist dangerous commands
    dangerous_commands = [
        'rm -rf', 'format', 'dd if=', 'dd of=', 'mkfs', 'fdisk',
        'su -', 'sudo', 'chmod 777', 'chown root', 'mount',
        'umount', 'reboot', 'shutdown', 'halt', 'poweroff'
    ]

    command_lower = command.lower()
    for dangerous in dangerous_commands:
        if dangerous in command_lower:
            return None, f"Command contains restricted operation: {dangerous}"

    # Limit command length
    if len(command) > 500:
        return None, "Command too long (max 500 characters)"

    logger.info(f"Executing shell command on {serial}: {command}")

    code, out, err = run_adb_command(['-s', serial, 'shell', command], timeout=timeout)

    return {
        'command': command,
        'return_code': code,
        'stdout': out,
        'stderr': err,
        'success': code == 0
    }, None


def get_file_from_device(serial, device_path, local_path=None):
    """Download a file from the device."""
    if not validate_device_serial(serial):
        return None, "Invalid device serial"

    # Sanitize device path
    device_path = sanitize_input(device_path, max_length=500)

    # Security: Restrict access to certain paths
    restricted_paths = [
        '/system/', '/root/', '/data/data/', '/proc/', '/sys/',
        '/dev/', '/etc/passwd', '/etc/shadow'
    ]

    for restricted in restricted_paths:
        if device_path.startswith(restricted):
            return None, f"Access to {restricted} is restricted"

    # Set local path if not provided
    if not local_path:
        filename = os.path.basename(device_path) or 'downloaded_file'
        local_path = os.path.join(UPLOAD_DIR, filename)

    logger.info(f"Downloading file from {serial}: {device_path} -> {local_path}")

    code, out, err = run_adb_command(['-s', serial, 'pull', device_path, local_path], timeout=60)

    if code == 0 and os.path.exists(local_path):
        file_size = os.path.getsize(local_path)
        return {
            'device_path': device_path,
            'local_path': local_path,
            'file_size': file_size,
            'success': True
        }, None
    else:
        return None, f"File download failed: {err}"


def put_file_to_device(serial, local_path, device_path):
    """Upload a file to the device."""
    if not validate_device_serial(serial):
        return None, "Invalid device serial"

    if not os.path.exists(local_path):
        return None, "Local file not found"

    # Sanitize device path
    device_path = sanitize_input(device_path, max_length=500)

    # Security: Restrict upload to certain paths
    allowed_paths = [
        '/sdcard/', '/data/local/tmp/', '/storage/emulated/0/'
    ]

    path_allowed = any(device_path.startswith(allowed) for allowed in allowed_paths)
    if not path_allowed:
        return None, f"Upload to {device_path} is not allowed. Use /sdcard/ or /data/local/tmp/"

    logger.info(f"Uploading file to {serial}: {local_path} -> {device_path}")

    code, out, err = run_adb_command(['-s', serial, 'push', local_path, device_path], timeout=120)

    if code == 0:
        return {
            'local_path': local_path,
            'device_path': device_path,
            'success': True
        }, None
    else:
        return None, f"File upload failed: {err}"


def modify_system_setting(serial, setting_name, value):
    """Modify a system setting on the device."""
    if not validate_device_serial(serial):
        return None, "Invalid device serial"

    # Sanitize inputs
    setting_name = sanitize_input(setting_name, max_length=100)
    value = sanitize_input(str(value), max_length=100)

    # Whitelist allowed settings for security
    allowed_settings = [
        'stay_on_while_plugged_in',
        'screen_brightness',
        'screen_off_timeout',
        'wifi_sleep_policy',
        'auto_time',
        'auto_time_zone',
        'accelerometer_rotation',
        'user_rotation',
        'window_animation_scale',
        'transition_animation_scale',
        'animator_duration_scale'
    ]

    if setting_name not in allowed_settings:
        return None, f"Setting '{setting_name}' is not in the allowed list"

    logger.info(f"Modifying system setting on {serial}: {setting_name} = {value}")

    # Use settings command to modify the setting
    code, out, err = run_adb_command([
        '-s', serial, 'shell', 'settings', 'put', 'system', setting_name, value
    ], timeout=15)

    if code == 0:
        return {
            'setting_name': setting_name,
            'value': value,
            'success': True
        }, None
    else:
        return None, f"Failed to modify setting: {err}"


def get_system_setting(serial, setting_name):
    """Get a system setting value from the device."""
    if not validate_device_serial(serial):
        return None, "Invalid device serial"

    setting_name = sanitize_input(setting_name, max_length=100)

    code, out, err = run_adb_command([
        '-s', serial, 'shell', 'settings', 'get', 'system', setting_name
    ], timeout=10)

    if code == 0:
        return {
            'setting_name': setting_name,
            'value': out.strip(),
            'success': True
        }, None
    else:
        return None, f"Failed to get setting: {err}"


# Batch Operations API Routes

@app.route('/api/batch/install', methods=['POST'])
@auth.login_required
@limiter.limit("10 per minute")
@handle_adb_errors
def batch_install_apks():
    """Install APKs on multiple devices simultaneously."""
    if 'apk' not in request.files:
        return jsonify(error="No APK file provided"), 400
    if 'devices' not in request.form:
        return jsonify(error="No target devices specified"), 400

    apk_file = request.files['apk']
    if not allowed_file(apk_file.filename):
        return jsonify(error="Invalid file type"), 400

    # Save APK temporarily
    filename = secure_filename(apk_file.filename)
    apk_path = os.path.join(UPLOAD_DIR, filename)
    apk_file.save(apk_path)

    # Get target devices
    try:
        target_devices = json.loads(request.form['devices'])
        if not isinstance(target_devices, list):
            raise ValueError("Devices must be a list")
    except (json.JSONDecodeError, ValueError) as e:
        os.remove(apk_path)
        return jsonify(error=f"Invalid devices format: {str(e)}"), 400

    # Installation options
    grant_permissions = request.form.get('grant_permissions', 'true').lower() == 'true'
    replace_existing = request.form.get('replace_existing', 'true').lower() == 'true'

    # Perform installations
    results = {}
    for serial in target_devices:
        if not validate_device_serial(serial):
            results[serial] = {'success': False, 'error': 'Invalid device serial'}
            continue

        install_result, error = install_multiple_apks(
            serial,
            [apk_path],
            grant_permissions=grant_permissions,
            replace=replace_existing
        )

        if error:
            results[serial] = {'success': False, 'error': error}
        else:
            results[serial] = {
                'success': True,
                'details': install_result[0] if install_result else None
            }

    # Clean up
    os.remove(apk_path)
    return jsonify(results)

@app.route('/api/batch/uninstall', methods=['POST'])
@auth.login_required
@limiter.limit("10 per minute")
@handle_adb_errors
def batch_uninstall_package():
    """Uninstall a package from multiple devices simultaneously."""
    data = request.get_json()
    if not data:
        return jsonify(error="No JSON data provided"), 400

    package_name = data.get('package_name')
    target_devices = data.get('devices', [])
    keep_data = data.get('keep_data', False)

    if not package_name or not target_devices:
        return jsonify(error="Package name and target devices are required"), 400

    if not isinstance(target_devices, list):
        return jsonify(error="Devices must be a list"), 400

    results = {}
    for serial in target_devices:
        if not validate_device_serial(serial):
            results[serial] = {'success': False, 'error': 'Invalid device serial'}
            continue

        success, message = uninstall_package(serial, package_name, keep_data)
        results[serial] = {'success': success, 'message': message}

    return jsonify(results)

@app.route('/api/batch/enable_wireless', methods=['POST'])
@auth.login_required
@limiter.limit("5 per minute")
@handle_adb_errors
def batch_enable_wireless():
    """Enable wireless ADB on multiple devices simultaneously."""
    data = request.get_json()
    if not data or not isinstance(data.get('devices', []), list):
        return jsonify(error="Invalid or missing devices list"), 400

    target_devices = data['devices']
    port = data.get('port', 5555)

    results = {}
    for serial in target_devices:
        if not validate_device_serial(serial):
            results[serial] = {'success': False, 'error': 'Invalid device serial'}
            continue

        success, message = enable_wireless_adb(serial, port)
        results[serial] = {'success': success, 'message': message}

    return jsonify(results)

# Web Interface Routes
@app.route('/static/<path:filename>')
def serve_static(filename):
    return send_from_directory(STATIC_DIR, filename)

@app.route('/advanced')
def advanced_dashboard():
    """Serve the advanced device management dashboard."""
    return render_template_string(open('templates/advanced_dashboard.html').read())

@app.route('/')
def index():
    """Redirect to advanced dashboard."""
    return redirect('/advanced')

# API Routes
@app.route('/api/usb-devices')
@auth.login_required
@limiter.limit("30/minute")
@swag_from({
    'tags': ['Devices'],
    'summary': 'Get list of connected USB devices',
    'description': 'Returns a list of all connected USB devices with enhanced detection, independent of ADB status.',
    'responses': {
        '200': {
            'description': 'List of connected USB devices',
            'schema': {
                'type': 'object',
                'properties': {
                    'success': {'type': 'boolean'},
                    'devices': {
                        'type': 'array',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'id': {'type': 'string', 'description': 'Device instance ID'},
                                'name': {'type': 'string', 'description': 'Device friendly name'},
                                'manufacturer': {'type': 'string', 'description': 'Device manufacturer'},
                                'vendor_id': {'type': 'string', 'description': 'Vendor ID'},
                                'product_id': {'type': 'string', 'description': 'Product ID'},
                                'vendor_name': {'type': 'string', 'description': 'Vendor name'},
                                'is_android': {'type': 'boolean', 'description': 'Whether device is Android'},
                                'connection_state': {'type': 'string', 'description': 'Device connection state'},
                                'capabilities': {
                                    'type': 'object',
                                    'properties': {
                                        'adb': {'type': 'boolean'},
                                        'mtp': {'type': 'boolean'},
                                        'fastboot': {'type': 'boolean'},
                                        'mass_storage': {'type': 'boolean'}
                                    }
                                }
                            }
                        }
                    },
                    'count': {'type': 'integer'}
                }
            }
        },
        '500': {
            'description': 'Internal server error',
            'schema': {
                'type': 'object',
                'properties': {
                    'success': {'type': 'boolean'},
                    'error': {'type': 'string'}
                }
            }
        }
    },
    'security': [{'basicAuth': []}]
})
def get_usb_devices_api():
    """Get list of connected USB devices with enhanced detection."""
    try:
        devices, error = usb_manager.get_usb_devices()

        if error:
            logger.error(f"Error getting USB devices: {error}")
            return jsonify({
                'success': False,
                'error': str(error),
                'devices': []
            }), 500

        # Process device information for API response
        processed_devices = []
        for device in devices:
            processed_device = {
                'id': device.get('instance_id'),
                'name': device.get('friendly_name', 'Unknown Device'),
                'manufacturer': device.get('manufacturer', 'Unknown'),
                'vendor_id': device.get('vendor_id'),
                'product_id': device.get('product_id'),
                'vendor_name': device.get('vendor_name', 'Unknown'),
                'is_android': device.get('is_android', False),
                'connection_state': device.get('connection_state', 'unknown'),
                'capabilities': {
                    'adb': device.get('adb_capable', False),
                    'mtp': device.get('mtp_capable', False),
                    'fastboot': device.get('fastboot_capable', False),
                    'mass_storage': device.get('mass_storage_capable', False)
                }
            }
            processed_devices.append(processed_device)

        return jsonify({
            'success': True,
            'devices': processed_devices,
            'count': len(processed_devices)
        })

    except Exception as e:
        logger.error(f"Error in USB devices API: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'devices': []
        }), 500

@app.route('/api/devices')
@auth.login_required
@limiter.limit("30/minute")
@swag_from({
    'tags': ['Devices'],
    'summary': 'Get list of connected devices',
    'description': 'Returns a list of all connected Android devices with their status and detailed information',
    'responses': {
        '200': {
            'description': 'List of connected devices',
            'schema': {
                'type': 'object',
                'properties': {
                    'devices': {
                        'type': 'array',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'serial': {'type': 'string', 'description': 'Device serial number'},
                                'state': {'type': 'string', 'description': 'Device connection state'},
                                'model': {'type': 'string', 'description': 'Device model name'},
                                'android_version': {'type': 'string', 'description': 'Android OS version'},
                                'manufacturer': {'type': 'string', 'description': 'Device manufacturer'}
                            }
                        }
                    }
                }
            }
        },
        '401': {
            'description': 'Authentication required',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'example': 'Unauthorized access'}
                }
            }
        },
        '429': {
            'description': 'Too many requests',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'example': 'Rate limit exceeded'}
                }
            }
        },
        '500': {
            'description': 'Internal server error',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string'}
                }
            }
        }
    },
    'security': [{'basicAuth': []}]
})
def get_devices_api():
    devices, err = get_connected_devices()
    if err:
        return jsonify(error=str(err)), 500
    
    # Enhance device information
    enhanced_devices = []
    for device in devices:
        if device['state'] == 'device':
            info, _ = get_comprehensive_device_info(device['serial'])
            if info:
                device.update(info)
        enhanced_devices.append(device)
    
    return jsonify(devices=enhanced_devices)

def get_device_health_info(serial):
    """Get device health information including battery, memory, CPU, and process stats."""
    if not validate_device_serial(serial):
        return None, "Invalid device serial"

    health_info = {'serial': serial, 'timestamp': datetime.now().isoformat()}

    # Get battery info
    code, out, _ = run_adb_command(['-s', serial, 'shell', 'dumpsys', 'battery'], timeout=10)
    if code == 0:
        for line in out.splitlines():
            if 'level:' in line:
                health_info['battery_level'] = int(line.split(':')[1].strip())
            elif 'status:' in line:
                health_info['battery_status'] = line.split(':')[1].strip()
            elif 'temperature:' in line:
                health_info['battery_temp'] = float(line.split(':')[1].strip()) / 10  # Convert to Celsius

    # Get memory info
    code, out, _ = run_adb_command(['-s', serial, 'shell', 'dumpsys', 'meminfo'], timeout=10)
    if code == 0:
        for line in out.splitlines():
            if 'Total RAM:' in line:
                health_info['total_ram'] = line.split(':')[1].strip()
            elif 'Free RAM:' in line:
                health_info['free_ram'] = line.split(':')[1].strip()
            elif 'Used RAM:' in line:
                health_info['used_ram'] = line.split(':')[1].strip()

    # Get CPU info
    code, out, _ = run_adb_command(['-s', serial, 'shell', 'cat', '/proc/cpuinfo'], timeout=10)
    if code == 0:
        cpu_cores = 0
        for line in out.splitlines():
            if 'processor' in line:
                cpu_cores += 1
        health_info['cpu_cores'] = cpu_cores

    # Get CPU usage
    code, out, _ = run_adb_command(['-s', serial, 'shell', 'top', '-n', '1', '-b'], timeout=10)
    if code == 0:
        cpu_usage = []
        for line in out.splitlines():
            if '%CPU' in line:
                continue
            parts = line.split()
            if len(parts) >= 9:
                try:
                    cpu = float(parts[8].replace('%', ''))
                    if cpu > 0:
                        cpu_usage.append({
                            'process': parts[9],
                            'cpu_usage': cpu,
                            'pid': parts[0]
                        })
                except (ValueError, IndexError):
                    continue
        health_info['top_cpu_processes'] = sorted(cpu_usage, key=lambda x: x['cpu_usage'], reverse=True)[:5]

    # Get running processes count
    code, out, _ = run_adb_command(['-s', serial, 'shell', 'ps', '-A'], timeout=10)
    if code == 0:
        health_info['process_count'] = len(out.splitlines()) - 1  # Subtract header line

    return health_info, None

@app.route('/api/devices/<serial>/info')
@auth.login_required
@limiter.limit("30/minute")
@swag_from({
    'tags': ['Devices'],
    'summary': 'Get detailed device information',
    'description': 'Returns comprehensive information about a specific device including hardware, software, storage, network, and health details',
    'parameters': [
        {
            'name': 'serial',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'Device serial number'
        }
    ],
    'responses': {
        '200': {
            'description': 'Device information',
            'schema': {
                'type': 'object',
                'properties': {
                    'device_info': {
                        'type': 'object',
                        'properties': {
                            'serial': {'type': 'string', 'description': 'Device serial number'},
                            'model': {'type': 'string', 'description': 'Device model'},
                            'manufacturer': {'type': 'string', 'description': 'Device manufacturer'},
                            'android_version': {'type': 'string', 'description': 'Android OS version'},
                            'api_level': {'type': 'string', 'description': 'Android API level'},
                            'storage': {
                                'type': 'object',
                                'properties': {
                                    'internal_total_kb': {'type': 'integer', 'description': 'Total internal storage in KB'},
                                    'internal_used_kb': {'type': 'integer', 'description': 'Used internal storage in KB'},
                                    'internal_available_kb': {'type': 'integer', 'description': 'Available internal storage in KB'},
                                    'internal_usage_percent': {'type': 'number', 'description': 'Storage usage percentage'}
                                }
                            },
                            'network': {
                                'type': 'object',
                                'properties': {
                                    'wifi_enabled': {'type': 'boolean', 'description': 'WiFi status'},
                                    'wifi_ip': {'type': 'string', 'description': 'WiFi IP address'},
                                    'mobile_enabled': {'type': 'boolean', 'description': 'Mobile data status'}
                                }
                            },
                            'health': {
                                'type': 'object',
                                'properties': {
                                    'battery_level': {'type': 'integer', 'description': 'Battery level percentage'},
                                    'battery_status': {'type': 'string', 'description': 'Charging status'},
                                    'battery_temperature': {'type': 'number', 'description': 'Battery temperature'},
                                    'memory_total': {'type': 'integer', 'description': 'Total RAM in KB'},
                                    'memory_available': {'type': 'integer', 'description': 'Available RAM in KB'},
                                    'cpu_cores': {'type': 'integer', 'description': 'Number of CPU cores'},
                                    'top_processes': {
                                        'type': 'array',
                                        'items': {
                                            'type': 'object',
                                            'properties': {
                                                'name': {'type': 'string', 'description': 'Process name'},
                                                'cpu_usage': {'type': 'number', 'description': 'CPU usage percentage'}
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        '400': {
            'description': 'Invalid request',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'example': 'Invalid device serial'}
                }
            }
        },
        '401': {
            'description': 'Authentication required',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'example': 'Unauthorized access'}
                }
            }
        },
        '429': {
            'description': 'Too many requests',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'example': 'Rate limit exceeded'}
                }
            }
        }
    },
    'security': [{'basicAuth': []}]
})
def get_device_info_api(serial):
    if not validate_device_serial(serial):
        return jsonify(error="Invalid device serial"), 400

    info, err = get_comprehensive_device_info(serial)
    if err:
        return jsonify(error=str(err)), 400

    storage, _ = get_device_storage_info(serial)
    if storage:
        info['storage'] = storage

    network, _ = get_device_network_info(serial)
    if network:
        info['network'] = network

    health, _ = get_device_health_info(serial)
    if health:
        info['health'] = health

    return jsonify(device_info=info)

@app.route('/api/devices/<serial>/packages')
@auth.login_required
@limiter.limit("30/minute")
@swag_from({
    'tags': ['Packages'],
    'summary': 'Get installed packages on device',
    'description': 'Returns a list of installed packages on the specified device with version information',
    'parameters': [
        {
            'name': 'serial',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'Device serial number'
        },
        {
            'name': 'include_system',
            'in': 'query',
            'type': 'boolean',
            'required': False,
            'default': False,
            'description': 'Include system packages in the results'
        }
    ],
    'responses': {
        '200': {
            'description': 'List of installed packages',
            'schema': {
                'type': 'object',
                'properties': {
                    'packages': {
                        'type': 'array',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'name': {'type': 'string', 'description': 'Package name'},
                                'version': {'type': 'string', 'description': 'Package version'}
                            }
                        }
                    }
                }
            }
        },
        '400': {
            'description': 'Invalid request',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'example': 'Invalid device serial'}
                }
            }
        },
        '401': {
            'description': 'Authentication required',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'example': 'Unauthorized access'}
                }
            }
        },
        '429': {
            'description': 'Too many requests',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'example': 'Rate limit exceeded'}
                }
            }
        }
    },
    'security': [{'basicAuth': []}]
})
def get_device_packages_api(serial):
    if not validate_device_serial(serial):
        return jsonify(error="Invalid device serial"), 400

    packages, err = get_installed_packages(serial)
    if err:
        return jsonify(error=str(err)), 400

    return jsonify(packages=packages)

@app.route('/api/install', methods=['POST'])
@auth.login_required
@limiter.limit("10/minute")
@swag_from({
    'tags': ['APK Installation'],
    'summary': 'Install APK on device(s)',
    'description': 'Upload and install APK file(s) on one or multiple devices',
    'parameters': [
        {
            'name': 'apk_file',
            'in': 'formData',
            'type': 'file',
            'required': True,
            'description': 'APK file to install'
        },
        {
            'name': 'serials',
            'in': 'formData',
            'type': 'array',
            'items': {'type': 'string'},
            'required': True,
            'description': 'List of device serial numbers to install the APK on'
        },
        {
            'name': 'grant_permissions',
            'in': 'formData',
            'type': 'boolean',
            'default': True,
            'description': 'Automatically grant required permissions after installation'
        },
        {
            'name': 'replace',
            'in': 'formData',
            'type': 'boolean',
            'default': True,
            'description': 'Replace existing app if already installed'
        }
    ],
    'consumes': ['multipart/form-data'],
    'responses': {
        '200': {
            'description': 'Installation results',
            'schema': {
                'type': 'object',
                'properties': {
                    'results': {
                        'type': 'array',
                        'items': {
                            'type': 'object',
                            'properties': {
                                'serial': {'type': 'string', 'description': 'Device serial number'},
                                'success': {'type': 'boolean', 'description': 'Installation success status'},
                                'message': {'type': 'string', 'description': 'Installation result message'},
                                'error': {'type': 'string', 'description': 'Error message if installation failed'}
                            }
                        }
                    }
                }
            }
        },
        '400': {
            'description': 'Invalid request',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'example': 'No APK file provided'}
                }
            }
        },
        '401': {
            'description': 'Authentication required',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'example': 'Unauthorized access'}
                }
            }
        },
        '429': {
            'description': 'Too many requests',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'example': 'Rate limit exceeded'}
                }
            }
        }
    },
    'security': [{'basicAuth': []}]
})
def install_apk_api():
    if 'apk' not in request.files:
        return jsonify(error="No APK file provided"), 400

    apk_file = request.files['apk']
    if not apk_file or not allowed_file(apk_file.filename):
        return jsonify(error="Invalid file type"), 400

    device_serial = request.form.get('device', 'all')
    grant_permissions = request.form.get('grantPermissions', 'true').lower() == 'true'

    # Save APK file
    filename = secure_filename(apk_file.filename)
    filepath = os.path.join(UPLOAD_DIR, filename)
    apk_file.save(filepath)

    try:
        if device_serial == 'all':
            devices, _ = get_connected_devices()
            results = []
            for device in devices:
                if device['state'] == 'device':
                    result, _ = install_multiple_apks(
                        device['serial'], [filepath], grant_permissions)
                    results.extend(result or [])
            return jsonify(results=results)
        else:
            if not validate_device_serial(device_serial):
                return jsonify(error="Invalid device serial"), 400

            results, err = install_multiple_apks(
                device_serial, [filepath], grant_permissions)
            if err:
                return jsonify(error=str(err)), 400

            return jsonify(results=results)
    finally:
        # Clean up uploaded file
        try:
            os.remove(filepath)
        except:
            pass

@app.route('/api/devices/<serial>/wireless', methods=['POST'])
@auth.login_required
@limiter.limit("5/minute")
def enable_wireless_api(serial):
    if not validate_device_serial(serial):
        return jsonify(error="Invalid device serial"), 400

    # Get device IP before enabling wireless
    ip_address, err = get_device_ip_address(serial)
    if err:
        return jsonify(error=f"Could not get device IP: {err}"), 400

    success, msg = enable_wireless_adb(serial)
    if not success:
        return jsonify(error=msg), 400

    return jsonify(success=True, ip_address=ip_address, message=msg)

@app.route('/api/devices/<serial>/packages/<package_name>', methods=['DELETE'])
@auth.login_required
@limiter.limit("10/minute")
def uninstall_package_api(serial, package_name):
    if not validate_device_serial(serial):
        return jsonify(error="Invalid device serial"), 400

    if not validate_package_name(package_name):
        return jsonify(error="Invalid package name"), 400

    keep_data = request.args.get('keepData', 'false').lower() == 'true'
    success, msg = uninstall_package(serial, package_name, keep_data)

    if not success:
        return jsonify(error=msg), 400

    return jsonify(success=True, message=msg)

def get_connected_devices():
    """Get list of connected Android devices."""
    code, out, err = run_adb_command(['devices'])
    if code != 0:
        logger.error(f"Failed to get devices: {err}")
        return None, err

    # Parse lines like: "serial\tdevice"
    lines = [l for l in out.splitlines() if l and not l.startswith('List of devices')]
    devices = []
    for l in lines:
        parts = l.split('\t')
        if len(parts) == 2:
            serial, state = parts
            devices.append({'serial': serial, 'state': state})

    logger.info(f"Found {len(devices)} connected devices")
    return devices, None


def detect_unauthorized_devices():
    """Detect devices that are connected but not authorized for ADB debugging."""
    devices, err = get_connected_devices()
    if err:
        return None, err

    unauthorized_devices = [d for d in devices if d['state'] in ['unauthorized', 'offline']]
    return unauthorized_devices, None


def attempt_adb_enable(serial):
    """Attempt to enable ADB debugging on a device through various methods."""
    if not validate_device_serial(serial):
        return False, "Invalid device serial"

    logger.info(f"Attempting to enable ADB debugging on device {serial}")

    # Method 1: Try to enable USB debugging via settings
    methods = [
        # Enable USB debugging via settings (requires device to be unlocked)
        ['shell', 'settings', 'put', 'global', 'adb_enabled', '1'],
        # Enable developer options
        ['shell', 'settings', 'put', 'global', 'development_settings_enabled', '1'],
        # Try to enable USB debugging via secure settings
        ['shell', 'settings', 'put', 'secure', 'adb_enabled', '1'],
        # Alternative method for some devices
        ['shell', 'setprop', 'service.adb.tcp.port', '5555'],
    ]

    success_count = 0
    for i, method in enumerate(methods, 1):
        try:
            code, out, err = run_adb_command(['-s', serial] + method, timeout=10)
            if code == 0:
                logger.info(f"Method {i} succeeded for enabling ADB on {serial}")
                success_count += 1
            else:
                logger.warning(f"Method {i} failed for {serial}: {err}")
        except Exception as e:
            logger.warning(f"Method {i} exception for {serial}: {str(e)}")

    if success_count > 0:
        # Wait a moment for settings to take effect
        import time
        time.sleep(2)

        # Verify if device is now authorized
        devices, _ = get_connected_devices()
        if devices:
            for device in devices:
                if device['serial'] == serial and device['state'] == 'device':
                    logger.info(f"Successfully enabled ADB debugging on {serial}")
                    return True, "ADB debugging enabled successfully"

        return True, f"ADB enable commands executed ({success_count}/{len(methods)} succeeded), but authorization may require manual confirmation on device"

    return False, "Failed to enable ADB debugging. Device may need to be unlocked and USB debugging manually enabled in Developer Options."


def smart_connect_device():
    """Smart device connection that attempts to enable ADB on unauthorized devices."""
    logger.info("Starting smart device connection process")

    # Get all connected devices
    devices, err = get_connected_devices()
    if err:
        return None, f"Failed to get devices: {err}"

    # Check for authorized devices first
    authorized_devices = [d for d in devices if d['state'] == 'device']
    if authorized_devices:
        if len(authorized_devices) == 1:
            return authorized_devices[0], "Device already authorized and ready"
        else:
            return None, f"Multiple authorized devices found ({len(authorized_devices)}). Please specify which device to use."

    # Look for unauthorized devices
    unauthorized_devices = [d for d in devices if d['state'] in ['unauthorized', 'offline']]
    if not unauthorized_devices:
        return None, "No devices connected. Please connect a device via USB cable."

    # Try to enable ADB on unauthorized devices
    results = []
    for device in unauthorized_devices:
        success, message = attempt_adb_enable(device['serial'])
        results.append({
            'serial': device['serial'],
            'success': success,
            'message': message
        })

    # Check again for newly authorized devices
    devices, err = get_connected_devices()
    if err:
        return None, f"Failed to re-check devices: {err}"

    authorized_devices = [d for d in devices if d['state'] == 'device']
    if authorized_devices:
        if len(authorized_devices) == 1:
            return authorized_devices[0], f"Successfully enabled ADB debugging on {authorized_devices[0]['serial']}"
        else:
            return None, f"Multiple devices now authorized. Please specify which device to use."

    # If still no authorized devices, return detailed results
    result_messages = [f"{r['serial']}: {r['message']}" for r in results]
    return None, f"ADB enable attempted on {len(unauthorized_devices)} device(s). Results: {'; '.join(result_messages)}"


def get_device_info(serial):
    """Get device information including Android version."""
    device_info = {'serial': serial}

    # Get Android version
    code, out, err = run_adb_command(['-s', serial, 'shell', 'getprop', 'ro.build.version.release'])
    if code == 0:
        device_info['android_version'] = out.strip()

    # Get device model
    code, out, err = run_adb_command(['-s', serial, 'shell', 'getprop', 'ro.product.model'])
    if code == 0:
        device_info['model'] = out.strip()

    # Get device manufacturer
    code, out, err = run_adb_command(['-s', serial, 'shell', 'getprop', 'ro.product.manufacturer'])
    if code == 0:
        device_info['manufacturer'] = out.strip()

    return device_info


def get_running_processes(serial):
    """Get list of running processes on the device."""
    code, out, err = run_adb_command(['-s', serial, 'shell', 'ps', '-A'], timeout=30)
    if code != 0:
        # Try alternative ps command for older Android versions
        code, out, err = run_adb_command(['-s', serial, 'shell', 'ps'], timeout=30)
        if code != 0:
            logger.error(f"Failed to get processes for device {serial}: {err}")
            return None, err

    processes = []
    lines = out.splitlines()

    # Skip header line if present
    if lines and ('PID' in lines[0] or 'USER' in lines[0]):
        lines = lines[1:]

    for line in lines:
        if not line.strip():
            continue

        # Parse ps output - format varies by Android version
        parts = line.split()
        if len(parts) >= 8:
            try:
                process = {
                    'user': parts[0],
                    'pid': int(parts[1]),
                    'ppid': int(parts[2]) if parts[2].isdigit() else 0,
                    'name': parts[-1],  # Process name is usually the last column
                    'cmd': ' '.join(parts[8:]) if len(parts) > 8 else parts[-1]
                }

                # Try to extract package name from process name
                if '.' in process['name'] and not process['name'].startswith('/'):
                    process['package_name'] = process['name']
                elif ':' in process['name']:
                    # Handle process names like "com.example.app:service"
                    process['package_name'] = process['name'].split(':')[0]
                else:
                    process['package_name'] = None

                processes.append(process)
            except (ValueError, IndexError) as e:
                logger.warning(f"Failed to parse process line: {line} - {e}")
                continue

    logger.info(f"Found {len(processes)} processes on device {serial}")
    return processes, None


def get_process_memory_info(serial, package_name):
    """Get memory information for a specific package."""
    if not validate_package_name(package_name):
        return None, "Invalid package name"

    code, out, err = run_adb_command(['-s', serial, 'shell', 'dumpsys', 'meminfo', package_name], timeout=30)
    if code != 0:
        return None, f"Failed to get memory info: {err}"

    memory_info = {}
    lines = out.splitlines()

    for line in lines:
        line = line.strip()
        if 'Total PSS:' in line:
            # Extract PSS memory in KB
            parts = line.split()
            if len(parts) >= 3:
                try:
                    memory_info['pss_kb'] = int(parts[2].replace(',', ''))
                except ValueError:
                    pass
        elif 'Total RAM:' in line:
            parts = line.split()
            if len(parts) >= 3:
                try:
                    memory_info['ram_kb'] = int(parts[2].replace(',', ''))
                except ValueError:
                    pass

    return memory_info, None


def get_package_pid(serial, package_name):
    """Get the PID of a specific package."""
    if not validate_package_name(package_name):
        return None, "Invalid package name"

    code, out, err = run_adb_command(['-s', serial, 'shell', 'pidof', package_name], timeout=10)
    if code == 0 and out.strip():
        try:
            return int(out.strip().split()[0]), None  # Take first PID if multiple
        except ValueError:
            pass

    # Fallback: search in ps output
    processes, err = get_running_processes(serial)
    if err:
        return None, err

    for process in processes:
        if process.get('package_name') == package_name:
            return process['pid'], None

    return None, "Package not running"


def stop_application(serial, package_name):
    """Stop an application using am force-stop."""
    if not validate_package_name(package_name):
        return False, "Invalid package name"

    logger.info(f"Force stopping application {package_name} on device {serial}")
    code, out, err = run_adb_command(['-s', serial, 'shell', 'am', 'force-stop', package_name], timeout=30)

    if code == 0:
        logger.info(f"Successfully stopped {package_name}")
        return True, "Application stopped successfully"
    else:
        logger.error(f"Failed to stop {package_name}: {err}")
        return False, f"Failed to stop application: {err}"


def kill_process_by_pid(serial, pid):
    """Kill a process by PID."""
    if not validate_pid(pid):
        return False, "Invalid PID"

    logger.info(f"Killing process {pid} on device {serial}")
    code, out, err = run_adb_command(['-s', serial, 'shell', 'kill', str(pid)], timeout=10)

    if code == 0:
        logger.info(f"Successfully killed process {pid}")
        return True, "Process killed successfully"
    else:
        # Try with kill -9 for force kill
        code, out, err = run_adb_command(['-s', serial, 'shell', 'kill', '-9', str(pid)], timeout=10)
        if code == 0:
            logger.info(f"Successfully force killed process {pid}")
            return True, "Process force killed successfully"
        else:
            logger.error(f"Failed to kill process {pid}: {err}")
            return False, f"Failed to kill process: {err}"


@app.route('/api/devices/<serial>/debug/enable', methods=['POST'])
@auth.login_required
@limiter.limit("10/minute")
@handle_adb_errors
@swag_from({
    'tags': ['Device Debugging'],
    'summary': 'Enable USB debugging on device',
    'description': 'Enables USB debugging on the specified device and guides through the authorization process',
    'parameters': [
        {
            'name': 'serial',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'Device serial number'
        }
    ],
    'responses': {
        '200': {
            'description': 'USB debugging enabled successfully',
            'schema': {
                'type': 'object',
                'properties': {
                    'success': {'type': 'boolean'},
                    'message': {'type': 'string'},
                    'requires_confirmation': {'type': 'boolean'},
                    'status': {
                        'type': 'object',
                        'properties': {
                            'debugging_enabled': {'type': 'boolean'},
                            'device_authorized': {'type': 'boolean'},
                            'developer_options': {'type': 'boolean'},
                            'error_details': {
                                'type': 'array',
                                'items': {'type': 'string'}
                            }
                        }
                    }
                }
            }
        },
        '400': {
            'description': 'Invalid request',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string'}
                }
            }
        }
    },
    'security': [{'basicAuth': []}]
})
def enable_device_debugging(serial):
    """Enable ADB debugging on a device and guide through the authorization process."""
    try:
        # Initialize ADB manager
        adb_manager = ADBManager(ADB_PATH)

        # Enable debugging
        success, message, requires_confirmation = adb_manager.enable_debugging(serial)

        # Get current debug status
        status, error = adb_manager.get_device_debug_status(serial)
        if error:
            logger.warning(f"Failed to get debug status after enabling: {error}")

        response = {
            'success': success,
            'message': message,
            'requires_confirmation': requires_confirmation,
            'status': status if status else {}
        }

        if not success:
            return jsonify({'error': message}), 400

        return jsonify(response)

    except Exception as e:
        logger.error(f"Error enabling device debugging: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/devices/<serial>/debug/disable', methods=['POST'])
@auth.login_required
@limiter.limit("10/minute")
@handle_adb_errors
@swag_from({
    'tags': ['Device Debugging'],
    'summary': 'Disable USB debugging on device',
    'description': 'Disables USB debugging on the specified device',
    'parameters': [
        {
            'name': 'serial',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'Device serial number'
        }
    ],
    'responses': {
        '200': {
            'description': 'USB debugging disabled successfully',
            'schema': {
                'type': 'object',
                'properties': {
                    'success': {'type': 'boolean'},
                    'message': {'type': 'string'},
                    'status': {
                        'type': 'object',
                        'properties': {
                            'debugging_enabled': {'type': 'boolean'},
                            'device_authorized': {'type': 'boolean'},
                            'developer_options': {'type': 'boolean'},
                            'error_details': {
                                'type': 'array',
                                'items': {'type': 'string'}
                            }
                        }
                    }
                }
            }
        },
        '400': {
            'description': 'Invalid request',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string'}
                }
            }
        }
    },
    'security': [{'basicAuth': []}]
})
def disable_device_debugging(serial):
    """Disable USB debugging on a device."""
    try:
        # Initialize ADB manager
        adb_manager = ADBManager(ADB_PATH)

        # Disable debugging
        success, message = adb_manager.disable_debugging(serial)

        # Get current debug status
        status, error = adb_manager.get_device_debug_status(serial)
        if error:
            logger.warning(f"Failed to get debug status after disabling: {error}")

        response = {
            'success': success,
            'message': message,
            'status': status if status else {}
        }

        if not success:
            return jsonify({'error': message}), 400

        return jsonify(response)

    except Exception as e:
        logger.error(f"Error disabling device debugging: {str(e)}")
        return jsonify({'error': str(e)}), 500

    # Attempt to enable debugging
    success, msg = attempt_adb_enable(serial)
    requires_confirmation = 'manual confirmation' in msg.lower() if msg else True

    return jsonify(
        success=success,
        message=msg,
        requires_confirmation=requires_confirmation
    )

@app.route('/api/devices/<serial>/debug/status', methods=['GET'])
@auth.login_required
@limiter.limit("30/minute")
@handle_adb_errors
def get_debug_status_api(serial):
    """Get the current ADB debugging status of a device."""
    if not validate_device_serial(serial):
        return jsonify(error="Invalid device serial"), 400

    status, err = get_device_debug_status(serial)
    if err:
        return jsonify(error=str(err)), 500

    return jsonify(status)

@app.route('/api/devices/<serial>/debug/disable', methods=['POST'])
@auth.login_required
@limiter.limit("10/minute")
@handle_adb_errors
@swag_from({
    'tags': ['Device Debugging'],
    'summary': 'Disable USB debugging on device',
    'description': 'Disables USB debugging on the specified device',
    'parameters': [
        {
            'name': 'serial',
            'in': 'path',
            'type': 'string',
            'required': True,
            'description': 'Device serial number'
        }
    ],
    'responses': {
        '200': {
            'description': 'USB debugging disabled successfully',
            'schema': {
                'type': 'object',
                'properties': {
                    'success': {'type': 'boolean'},
                    'message': {'type': 'string'}
                }
            }
        },
        '400': {
            'description': 'Invalid request',
            'schema': {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string'}
                }
            }
        }
    },
    'security': [{'basicAuth': []}]
})
def disable_device_debugging(serial):
    """Disable USB debugging on a device."""
    try:
        # Initialize ADB manager
        adb_manager = ADBManager(ADB_PATH)

        # Disable debugging
        success, message = adb_manager.disable_debugging(serial)

        # Get current debug status
        status, error = adb_manager.get_device_debug_status(serial)
        if error:
            logger.warning(f"Failed to get debug status after disabling: {error}")

        response = {
            'success': success,
            'message': message,
            'status': status if status else {}
        }

        if not success:
            return jsonify({'error': message}), 400

        return jsonify(response)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error disabling USB debugging: {str(e)}'
        }), 500

def get_device_debug_status(serial):
    """Get comprehensive debug status information for a device."""
    if not validate_device_serial(serial):
        return None, "Invalid device serial"

    status = {
        'debugging_enabled': False,
        'device_authorized': False,
        'developer_options': False,
        'last_authorized': None,
        'adb_version': None,
        'usb_config': None,
        'error_details': []
    }

    # Check if device is in device list and authorized
    devices, _ = get_connected_devices()
    for device in devices:
        if device['serial'] == serial:
            status['device_authorized'] = device['state'] == 'device'
            break

    # Check developer options status
    code, out, _ = run_adb_command([
        '-s', serial, 'shell', 'settings', 'get', 'global', 'development_settings_enabled'
    ], timeout=10)
    if code == 0:
        status['developer_options'] = out.strip() == '1'

    # Check USB debugging status
    code, out, _ = run_adb_command([
        '-s', serial, 'shell', 'settings', 'get', 'global', 'adb_enabled'
    ], timeout=10)
    if code == 0:
        status['debugging_enabled'] = out.strip() == '1'

    # Get ADB version info
    code, out, _ = run_adb_command(['version'], timeout=10)
    if code == 0:
        status['adb_version'] = out.strip()

    # Get USB configuration
    code, out, _ = run_adb_command([
        '-s', serial, 'shell', 'getprop', 'sys.usb.config'
    ], timeout=10)
    if code == 0:
        status['usb_config'] = out.strip()

    # Check for common issues
    if not status['device_authorized']:
        if not status['debugging_enabled']:
            status['error_details'].append("USB debugging is not enabled in Developer options")
        if not status['developer_options']:
            status['error_details'].append("Developer options are not enabled")
        status['error_details'].append("Device needs to be authorized for USB debugging")

    return status, None

@app.route('/health', methods=['GET'])
def health():
    return jsonify(status='ok')


@app.route('/devices', methods=['GET'])
def list_devices():
    devices, err = get_connected_devices()
    if err:
        return jsonify(error='Failed to get devices', message=err), 500

    # Add device info for each connected device
    detailed_devices = []
    for device in devices:
        if device['state'] == 'device':
            device_info = get_device_info(device['serial'])
            detailed_devices.append(device_info)
        else:
            detailed_devices.append(device)

    return jsonify(devices=detailed_devices)


@app.route('/upload', methods=['POST'])
def upload_apk():
    """
    Form-data:
    - file: APK file
    - serial (optional): target device serial; if omitted and exactly one device is connected in 'device' state, it will be used
    - install (optional, default true): whether to install after upload
    - grant_permissions (optional, default true): pass -g to pm install (runtime permissions)
    - replace (optional, default true): replace existing application (-r)
    """
    if 'file' not in request.files:
        return jsonify(error='Missing file field'), 400
    f = request.files['file']
    if f.filename == '':
        return jsonify(error='Empty filename'), 400
    if not allowed_file(f.filename):
        return jsonify(error='Only .apk files are allowed'), 400

    filename = secure_filename(f.filename)
    save_path = os.path.join(UPLOAD_DIR, filename)
    f.save(save_path)

    # Determine serial
    serial = request.form.get('serial', '').strip()
    install = request.form.get('install', 'true').lower() != 'false'
    grant_permissions = request.form.get('grant_permissions', 'true').lower() != 'false'
    replace = request.form.get('replace', 'true').lower() != 'false'

    if not install:
        return jsonify(message='Uploaded', path=save_path), 200

    # If no serial, pick the only connected device in 'device' state
    if not serial:
        code, out, err = run_adb_command(['devices'])
        if code != 0:
            return jsonify(error='Failed to list devices', stderr=err), 500
        lines = [l for l in out.splitlines() if l and not l.startswith('List of devices')]
        device_lines = [l for l in lines if l.endswith('\tdevice')]
        if len(device_lines) == 0:
            return jsonify(error='No connected authorized devices'), 400
        if len(device_lines) > 1:
            return jsonify(error='Multiple devices connected. Specify serial.'), 400
        serial = device_lines[0].split('\t')[0]

    # Push APK to device temp location then install using pm (more reliable across versions)
    remote_path = f"/data/local/tmp/{filename}"

    # adb -s <serial> push file /data/local/tmp/
    code, out, err = run_adb_command(['-s', serial, 'push', save_path, remote_path])
    if code != 0:
        return jsonify(error='adb push failed', stdout=out, stderr=err), 500

    # Prepare pm install arguments
    pm_args = ['shell', 'pm', 'install']
    if replace:
        pm_args.append('-r')
    if grant_permissions:
        pm_args.append('-g')
    pm_args.append(remote_path)

    code, out, err = run_adb_command(['-s', serial] + pm_args, timeout=300)

    # Clean up temp file regardless of install result
    run_adb_command(['-s', serial, 'shell', 'rm', '-f', remote_path])

    if code != 0 or ('Success' not in out and 'Success' not in err):
        return jsonify(error='pm install failed', stdout=out, stderr=err), 500

    return jsonify(message='Installed successfully', serial=serial, stdout=out)


@app.route('/processes', methods=['GET'])
@handle_adb_errors
def list_processes():
    """List running processes on a device."""
    serial = request.args.get('serial', '').strip()

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Get processes
    processes, err = get_running_processes(serial)
    if err:
        return jsonify(error='Failed to get processes', message=err), 500

    # Filter to show only user applications if requested
    show_all = request.args.get('show_all', 'false').lower() == 'true'
    if not show_all:
        # Filter out system processes and show only user apps
        user_processes = []
        for proc in processes:
            if (proc.get('package_name') and
                '.' in proc['package_name'] and
                not proc['package_name'].startswith('com.android.') and
                not proc['package_name'].startswith('android.') and
                not proc['package_name'].startswith('system')):
                user_processes.append(proc)
        processes = user_processes

    return jsonify(serial=serial, processes=processes, count=len(processes))


@app.route('/processes/info', methods=['GET'])
@handle_adb_errors
def get_process_info():
    """Get detailed information about a specific process/package."""
    serial = request.args.get('serial', '').strip()
    package_name = request.args.get('package_name', '').strip()

    if not package_name:
        return jsonify(error='package_name parameter is required'), 400

    if not validate_package_name(package_name):
        return jsonify(error='Invalid package name'), 400

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Get PID
    pid, err = get_package_pid(serial, package_name)
    if err:
        return jsonify(error='Package not running', message=err), 404

    # Get memory info
    memory_info, mem_err = get_process_memory_info(serial, package_name)

    result = {
        'serial': serial,
        'package_name': package_name,
        'pid': pid,
        'memory_info': memory_info if not mem_err else None,
        'memory_error': mem_err
    }

    return jsonify(result)


@app.route('/processes/stop', methods=['POST'])
@handle_adb_errors
def stop_process():
    """Stop an application using am force-stop."""
    data = request.get_json() or {}
    serial = data.get('serial', '').strip()
    package_name = data.get('package_name', '').strip()

    if not package_name:
        return jsonify(error='package_name is required'), 400

    if not validate_package_name(package_name):
        return jsonify(error='Invalid package name'), 400

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    success, message = stop_application(serial, package_name)

    if success:
        return jsonify(message=message, serial=serial, package_name=package_name)
    else:
        return jsonify(error=message, serial=serial, package_name=package_name), 500


@app.route('/processes/kill', methods=['POST'])
@handle_adb_errors
def kill_process():
    """Kill a process by PID."""
    data = request.get_json() or {}
    serial = data.get('serial', '').strip()
    pid = data.get('pid')

    if not pid:
        return jsonify(error='pid is required'), 400

    if not validate_pid(pid):
        return jsonify(error='Invalid PID'), 400

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    success, message = kill_process_by_pid(serial, pid)

    if success:
        return jsonify(message=message, serial=serial, pid=pid)
    else:
        return jsonify(error=message, serial=serial, pid=pid), 500


@app.route('/devices/info', methods=['GET'])
@handle_adb_errors
def get_device_detailed_info():
    """Get comprehensive device information."""
    serial = request.args.get('serial', '').strip()

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Get comprehensive device info
    device_info, err = get_comprehensive_device_info(serial)
    if err:
        return jsonify(error=err), 500

    return jsonify(device_info)


@app.route('/devices/storage', methods=['GET'])
@handle_adb_errors
def get_device_storage():
    """Get device storage information."""
    serial = request.args.get('serial', '').strip()

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Get storage info
    storage_info, err = get_device_storage_info(serial)
    if err:
        return jsonify(error=err), 500

    return jsonify(serial=serial, storage=storage_info)


@app.route('/devices/packages', methods=['GET'])
@handle_adb_errors
def get_device_packages():
    """Get installed packages on the device."""
    serial = request.args.get('serial', '').strip()
    include_system = request.args.get('include_system', 'false').lower() == 'true'

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Get packages
    packages, err = get_installed_packages(serial, include_system)
    if err:
        return jsonify(error=err), 500

    return jsonify(serial=serial, packages=packages, count=len(packages))


@app.route('/devices/network', methods=['GET'])
@handle_adb_errors
def get_device_network():
    """Get device network information."""
    serial = request.args.get('serial', '').strip()

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Get network info
    network_info, err = get_device_network_info(serial)
    if err:
        return jsonify(error=err), 500

    return jsonify(serial=serial, network=network_info)


@app.route('/packages/download', methods=['POST'])
@handle_adb_errors
def download_package():
    """Download APK from device."""
    data = request.get_json() or {}
    serial = data.get('serial', '').strip()
    package_name = data.get('package_name', '').strip()

    if not package_name:
        return jsonify(error='package_name is required'), 400

    if not validate_package_name(package_name):
        return jsonify(error='Invalid package name'), 400

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Download APK
    result, err = download_apk_from_device(serial, package_name)
    if err:
        return jsonify(error=err), 500

    return jsonify(result)


@app.route('/packages/install-multiple', methods=['POST'])
@handle_adb_errors
def install_multiple():
    """Install multiple APK files."""
    if 'files' not in request.files:
        return jsonify(error='No files provided'), 400

    files = request.files.getlist('files')
    serial = request.form.get('serial', '').strip()
    grant_permissions = request.form.get('grant_permissions', 'true').lower() != 'false'
    replace = request.form.get('replace', 'true').lower() != 'false'

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Save uploaded files
    apk_paths = []
    for file in files:
        if file.filename and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            save_path = os.path.join(UPLOAD_DIR, filename)
            file.save(save_path)
            apk_paths.append(save_path)

    if not apk_paths:
        return jsonify(error='No valid APK files provided'), 400

    # Install APKs
    results, err = install_multiple_apks(serial, apk_paths, grant_permissions, replace)
    if err:
        return jsonify(error=err), 500

    return jsonify(serial=serial, results=results)


@app.route('/packages/uninstall', methods=['POST'])
@handle_adb_errors
def uninstall_package_endpoint():
    """Uninstall a package from device."""
    data = request.get_json() or {}
    serial = data.get('serial', '').strip()
    package_name = data.get('package_name', '').strip()
    keep_data = data.get('keep_data', False)

    if not package_name:
        return jsonify(error='package_name is required'), 400

    if not validate_package_name(package_name):
        return jsonify(error='Invalid package name'), 400

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Uninstall package
    success, message = uninstall_package(serial, package_name, keep_data)

    if success:
        return jsonify(message=message, serial=serial, package_name=package_name)
    else:
        return jsonify(error=message, serial=serial, package_name=package_name), 500


@app.route('/packages/info', methods=['GET'])
@handle_adb_errors
def get_package_info():
    """Get detailed information about a package."""
    serial = request.args.get('serial', '').strip()
    package_name = request.args.get('package_name', '').strip()

    if not package_name:
        return jsonify(error='package_name parameter is required'), 400

    if not validate_package_name(package_name):
        return jsonify(error='Invalid package name'), 400

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Get package info
    package_info, err = get_package_detailed_info(serial, package_name)
    if err:
        return jsonify(error=err), 500

    return jsonify(package_info)


@app.route('/wireless/enable', methods=['POST'])
@handle_adb_errors
def enable_wireless():
    """Enable wireless ADB on a device."""
    data = request.get_json() or {}
    serial = data.get('serial', '').strip()
    port = data.get('port', 5555)

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Enable wireless ADB
    success, message = enable_wireless_adb(serial, port)

    if success:
        return jsonify(message=message, serial=serial, port=port)
    else:
        return jsonify(error=message, serial=serial, port=port), 500


@app.route('/wireless/connect', methods=['POST'])
@handle_adb_errors
def connect_wireless():
    """Connect to a device over WiFi."""
    data = request.get_json() or {}
    ip_address = data.get('ip_address', '').strip()
    port = data.get('port', 5555)

    if not ip_address:
        return jsonify(error='ip_address is required'), 400

    # Connect to wireless device
    success, message = connect_wireless_device(ip_address, port)

    if success:
        return jsonify(message=message, ip_address=ip_address, port=port)
    else:
        return jsonify(error=message, ip_address=ip_address, port=port), 500


@app.route('/wireless/disconnect', methods=['POST'])
@handle_adb_errors
def disconnect_wireless():
    """Disconnect from a wireless device."""
    data = request.get_json() or {}
    ip_address = data.get('ip_address', '').strip()
    port = data.get('port', 5555)

    if not ip_address:
        return jsonify(error='ip_address is required'), 400

    # Disconnect from wireless device
    success, message = disconnect_wireless_device(ip_address, port)

    if success:
        return jsonify(message=message, ip_address=ip_address, port=port)
    else:
        return jsonify(error=message, ip_address=ip_address, port=port), 500


@app.route('/wireless/pair', methods=['POST'])
@handle_adb_errors
def pair_wireless():
    """Pair with a device using wireless debugging."""
    data = request.get_json() or {}
    ip_address = data.get('ip_address', '').strip()
    port = data.get('port')
    pairing_code = data.get('pairing_code', '').strip()

    if not ip_address or not pairing_code:
        return jsonify(error='ip_address and pairing_code are required'), 400

    if not port:
        return jsonify(error='port is required for pairing'), 400

    # Pair with wireless device
    success, message = pair_wireless_device(ip_address, port, pairing_code)

    if success:
        return jsonify(message=message, ip_address=ip_address, port=port)
    else:
        return jsonify(error=message, ip_address=ip_address, port=port), 500


@app.route('/wireless/get-ip', methods=['GET'])
@handle_adb_errors
def get_device_ip():
    """Get the IP address of a connected device."""
    serial = request.args.get('serial', '').strip()

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Get device IP
    ip_address, err = get_device_ip_address(serial)
    if err:
        return jsonify(error=err), 500

    return jsonify(serial=serial, ip_address=ip_address)


@app.route('/sim/info', methods=['GET'])
@handle_adb_errors
def get_sim_information():
    """Get SIM card information."""
    serial = request.args.get('serial', '').strip()

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Get SIM info
    sim_info, err = get_sim_info(serial)
    if err:
        return jsonify(error=err), 500

    return jsonify(serial=serial, sim_info=sim_info)


@app.route('/sim/toggle', methods=['POST'])
@handle_adb_errors
def toggle_sim():
    """Enable or disable a SIM card slot."""
    data = request.get_json() or {}
    serial = data.get('serial', '').strip()
    sim_slot = data.get('sim_slot')
    enable = data.get('enable', True)

    if sim_slot is None:
        return jsonify(error='sim_slot is required (0 or 1)'), 400

    try:
        sim_slot = int(sim_slot)
    except (ValueError, TypeError):
        return jsonify(error='sim_slot must be an integer (0 or 1)'), 400

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Toggle SIM
    success, message = toggle_sim_card(serial, sim_slot, enable)

    if success:
        return jsonify(message=message, serial=serial, sim_slot=sim_slot, enabled=enable)
    else:
        return jsonify(error=message, serial=serial, sim_slot=sim_slot), 500


@app.route('/sim/set-preferred', methods=['POST'])
@handle_adb_errors
def set_preferred_sim_endpoint():
    """Set preferred SIM for data, voice, or SMS."""
    data = request.get_json() or {}
    serial = data.get('serial', '').strip()
    sim_slot = data.get('sim_slot')
    service_type = data.get('service_type', 'data')

    if sim_slot is None:
        return jsonify(error='sim_slot is required (0 or 1)'), 400

    try:
        sim_slot = int(sim_slot)
    except (ValueError, TypeError):
        return jsonify(error='sim_slot must be an integer (0 or 1)'), 400

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Set preferred SIM
    success, message = set_preferred_sim(serial, sim_slot, service_type)

    if success:
        return jsonify(message=message, serial=serial, sim_slot=sim_slot, service_type=service_type)
    else:
        return jsonify(error=message, serial=serial, sim_slot=sim_slot, service_type=service_type), 500


@app.route('/system/shell', methods=['POST'])
@handle_adb_errors
def execute_shell():
    """Execute a shell command on the device."""
    data = request.get_json() or {}
    serial = data.get('serial', '').strip()
    command = data.get('command', '').strip()
    timeout = data.get('timeout', 30)

    if not command:
        return jsonify(error='command is required'), 400

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Execute shell command
    result, err = execute_shell_command(serial, command, timeout)
    if err:
        return jsonify(error=err), 400

    return jsonify(serial=serial, **result)


@app.route('/system/file/download', methods=['POST'])
@handle_adb_errors
def download_file():
    """Download a file from the device."""
    data = request.get_json() or {}
    serial = data.get('serial', '').strip()
    device_path = data.get('device_path', '').strip()

    if not device_path:
        return jsonify(error='device_path is required'), 400

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Download file
    result, err = get_file_from_device(serial, device_path)
    if err:
        return jsonify(error=err), 400

    return jsonify(serial=serial, **result)


@app.route('/system/file/upload', methods=['POST'])
@handle_adb_errors
def upload_file():
    """Upload a file to the device."""
    if 'file' not in request.files:
        return jsonify(error='No file provided'), 400

    file = request.files['file']
    serial = request.form.get('serial', '').strip()
    device_path = request.form.get('device_path', '').strip()

    if not device_path:
        return jsonify(error='device_path is required'), 400

    if file.filename == '':
        return jsonify(error='Empty filename'), 400

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Save uploaded file temporarily
    filename = secure_filename(file.filename)
    local_path = os.path.join(UPLOAD_DIR, filename)
    file.save(local_path)

    try:
        # Upload file to device
        result, err = put_file_to_device(serial, local_path, device_path)
        if err:
            return jsonify(error=err), 400

        return jsonify(serial=serial, **result)

    finally:
        # Clean up temporary file
        if os.path.exists(local_path):
            os.remove(local_path)


@app.route('/system/settings/get', methods=['GET'])
@handle_adb_errors
def get_setting():
    """Get a system setting value."""
    serial = request.args.get('serial', '').strip()
    setting_name = request.args.get('setting_name', '').strip()

    if not setting_name:
        return jsonify(error='setting_name parameter is required'), 400

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Get setting
    result, err = get_system_setting(serial, setting_name)
    if err:
        return jsonify(error=err), 400

    return jsonify(serial=serial, **result)


@app.route('/system/settings/set', methods=['POST'])
@handle_adb_errors
def set_setting():
    """Set a system setting value."""
    data = request.get_json() or {}
    serial = data.get('serial', '').strip()
    setting_name = data.get('setting_name', '').strip()
    value = data.get('value', '')

    if not setting_name:
        return jsonify(error='setting_name is required'), 400

    if value == '':
        return jsonify(error='value is required'), 400

    # Validate and get device
    serial, err = validate_and_get_device(serial)
    if err:
        return jsonify(error=err), 400

    # Set setting
    result, err = modify_system_setting(serial, setting_name, value)
    if err:
        return jsonify(error=err), 400

    return jsonify(serial=serial, **result)


@app.route('/debug/adb', methods=['GET'])
def debug_adb():
    """Debug endpoint to check ADB connectivity and status."""
    debug_info = {
        'adb_path': ADB_PATH,
        'timestamp': datetime.now().isoformat()
    }

    # Test ADB version
    code, out, err = run_adb_command(['version'], timeout=10)
    debug_info['adb_version'] = {
        'return_code': code,
        'stdout': out,
        'stderr': err
    }

    # Test device connectivity
    devices, device_err = get_connected_devices()
    debug_info['devices'] = {
        'devices': devices if not device_err else None,
        'error': device_err
    }

    # Check if any devices are connected and authorized
    if devices:
        for device in devices:
            if device['state'] == 'device':
                # Test basic shell command
                code, out, err = run_adb_command(['-s', device['serial'], 'shell', 'echo', 'test'], timeout=10)
                debug_info[f"device_{device['serial']}_shell_test"] = {
                    'return_code': code,
                    'stdout': out,
                    'stderr': err
                }

    logger.info(f"Debug ADB info requested: {debug_info}")
    return jsonify(debug_info)


@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors with JSON response for API calls."""
    if request.path.startswith('/api/') or request.headers.get('Content-Type') == 'application/json':
        return jsonify(error='Endpoint not found'), 404
    return jsonify(error='Page not found'), 404


@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors with proper logging."""
    logger.error(f"Internal server error: {str(error)}")
    return jsonify(error='Internal server error'), 500


# Comprehensive HTML Template for Android ADB Manager
COMPREHENSIVE_UI_HTML = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Android ADB Manager - Comprehensive Control Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            overflow-x: auto;
        }

        .nav-tab {
            padding: 15px 25px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .nav-tab:hover {
            background: #e9ecef;
            color: #333;
        }

        .nav-tab.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background: white;
        }

        .tab-content {
            display: none;
            padding: 30px;
            min-height: 600px;
        }

        .tab-content.active {
            display: block;
        }

        .device-selector {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 20px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        label {
            font-weight: 500;
            color: #333;
        }

        input, select, textarea {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-info:hover {
            background: #138496;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .status-message {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            display: none;
            font-weight: 500;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card h3 {
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }

        .info-item strong {
            display: block;
            color: #333;
            margin-bottom: 5px;
        }

        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .action-buttons button {
            padding: 5px 10px;
            font-size: 12px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 16px;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            transition: border-color 0.3s ease;
            cursor: pointer;
        }

        .file-upload:hover {
            border-color: #007bff;
        }

        .file-upload.dragover {
            border-color: #007bff;
            background: #f8f9ff;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            width: 0%;
            transition: width 0.3s ease;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #ddd;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #000;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 10px 0;
        }

        .wireless-setup {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .wireless-setup h4 {
            margin-bottom: 15px;
        }

        .security-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .security-warning strong {
            display: block;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                border-radius: 8px;
            }

            .header {
                padding: 15px;
            }

            .header h1 {
                font-size: 2em;
            }

            .tab-content {
                padding: 20px;
            }

            .device-selector {
                flex-direction: column;
                align-items: stretch;
            }

            .form-row {
                flex-direction: column;
            }

            .nav-tabs {
                flex-wrap: wrap;
            }

            .nav-tab {
                flex: 1;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <h1>🤖 Android ADB Manager</h1>
            <p>Comprehensive Android Device Management & Control Panel</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">📊 Overview</button>
            <button class="nav-tab" onclick="showTab('processes')">⚙️ Processes</button>
            <button class="nav-tab" onclick="showTab('packages')">📦 Packages</button>
            <button class="nav-tab" onclick="showTab('wireless')">📡 Wireless</button>
            <button class="nav-tab" onclick="showTab('system')">🔧 System</button>
            <button class="nav-tab" onclick="showTab('files')">📁 Files</button>
            <button class="nav-tab" onclick="showTab('debug')">🐛 Debug</button>
        </div>

        <div class="device-selector">
            <div class="form-group" style="margin-bottom: 0;">
                <label for="deviceSelect">Select Device:</label>
                <select id="deviceSelect" style="min-width: 200px;">
                    <option value="">Loading devices...</option>
                </select>
            </div>
            <button class="btn-primary" onclick="refreshDevices()">🔄 Refresh Devices</button>
            <button class="btn-info" onclick="showDeviceDetails()" id="deviceDetailsBtn" disabled>📋 Device Details</button>
            <div id="deviceStatus" class="status-message"></div>
        </div>

        <!-- Overview Tab -->
        <div id="overview" class="tab-content active">
            <div class="card">
                <h3>🏠 Device Overview</h3>
                <div id="deviceOverview">
                    <div class="loading">Select a device to view overview</div>
                </div>
            </div>

            <div class="card">
                <h3>🚀 Quick Actions</h3>
                <div class="form-row">
                    <button class="btn-info" onclick="getDeviceInfo()" disabled id="getInfoBtn">📊 Get Device Info</button>
                    <button class="btn-success" onclick="getStorageInfo()" disabled id="getStorageBtn">💾 Storage Info</button>
                    <button class="btn-warning" onclick="getNetworkInfo()" disabled id="getNetworkBtn">🌐 Network Info</button>
                    <button class="btn-primary" onclick="getInstalledPackages()" disabled id="getPackagesBtn">📦 List Packages</button>
                </div>
            </div>
        </div>

        <!-- Processes Tab -->
        <div id="processes" class="tab-content">
            <div class="card">
                <h3>⚙️ Process Management</h3>
                <div class="form-row">
                    <button class="btn-primary" onclick="loadProcesses()" disabled id="loadProcessesBtn">🔄 Load Processes</button>
                    <div class="checkbox-group">
                        <input type="checkbox" id="showAllProcesses">
                        <label for="showAllProcesses">Show system processes</label>
                    </div>
                </div>
                <div id="processContainer">
                    <div class="loading">Select a device and click Load Processes</div>
                </div>
            </div>
        </div>

        <!-- Packages Tab -->
        <div id="packages" class="tab-content">
            <div class="card">
                <h3>📦 Package Management</h3>

                <div class="card">
                    <h4>📤 Install APK</h4>
                    <div class="file-upload" onclick="document.getElementById('apkFile').click()">
                        <input type="file" id="apkFile" accept=".apk" style="display: none;" onchange="handleFileSelect(this)">
                        <p>📱 Click to select APK file or drag and drop</p>
                        <p style="color: #666; font-size: 14px;">Supports .apk files</p>
                    </div>
                    <div id="uploadProgress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <p id="uploadStatus">Uploading...</p>
                    </div>
                    <div class="form-row" style="margin-top: 15px;">
                        <div class="checkbox-group">
                            <input type="checkbox" id="grantPermissions" checked>
                            <label for="grantPermissions">Grant permissions automatically</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="replaceExisting" checked>
                            <label for="replaceExisting">Replace existing app</label>
                        </div>
                        <button class="btn-success" onclick="installAPK()" disabled id="installBtn">📲 Install APK</button>
                    </div>
                </div>

                <div class="card">
                    <h4>📋 Package Operations</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="packageName">Package Name:</label>
                            <input type="text" id="packageName" placeholder="com.example.app">
                        </div>
                        <button class="btn-info" onclick="getPackageInfo()">ℹ️ Get Info</button>
                        <button class="btn-warning" onclick="downloadPackage()">⬇️ Download APK</button>
                        <button class="btn-danger" onclick="uninstallPackage()">🗑️ Uninstall</button>
                    </div>
                </div>

                <div id="packageResults"></div>
            </div>
        </div>

        <!-- Wireless Tab -->
        <div id="wireless" class="tab-content">
            <div class="security-warning">
                <strong>⚠️ Security Notice:</strong>
                Wireless ADB connections are less secure than USB connections. Only use on trusted networks and disable when not needed.
            </div>

            <div class="card">
                <h3>📡 Wireless ADB Management</h3>

                <div class="wireless-setup">
                    <h4>🔧 Setup Wireless Connection</h4>
                    <p>First, enable wireless ADB on your device via USB, then connect wirelessly.</p>
                </div>

                <div class="card">
                    <h4>1️⃣ Enable Wireless ADB</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="wirelessPort">Port:</label>
                            <input type="number" id="wirelessPort" value="5555" min="1024" max="65535">
                        </div>
                        <button class="btn-primary" onclick="enableWireless()" disabled id="enableWirelessBtn">📡 Enable Wireless</button>
                        <button class="btn-info" onclick="getDeviceIP()" disabled id="getIPBtn">🌐 Get Device IP</button>
                    </div>
                </div>

                <div class="card">
                    <h4>2️⃣ Connect to Device</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="deviceIP">Device IP Address:</label>
                            <input type="text" id="deviceIP" placeholder="*************">
                        </div>
                        <div class="form-group">
                            <label for="connectPort">Port:</label>
                            <input type="number" id="connectPort" value="5555" min="1024" max="65535">
                        </div>
                        <button class="btn-success" onclick="connectWireless()">🔗 Connect</button>
                        <button class="btn-danger" onclick="disconnectWireless()">❌ Disconnect</button>
                    </div>
                </div>

                <div class="card">
                    <h4>🔐 Wireless Pairing (Android 11+)</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="pairIP">Pairing IP:</label>
                            <input type="text" id="pairIP" placeholder="*************">
                        </div>
                        <div class="form-group">
                            <label for="pairPort">Pairing Port:</label>
                            <input type="number" id="pairPort" placeholder="37829" min="1024" max="65535">
                        </div>
                        <div class="form-group">
                            <label for="pairingCode">Pairing Code:</label>
                            <input type="text" id="pairingCode" placeholder="123456" maxlength="6">
                        </div>
                        <button class="btn-warning" onclick="pairDevice()">🤝 Pair Device</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Tab -->
        <div id="system" class="tab-content">
            <div class="security-warning">
                <strong>⚠️ Warning:</strong>
                System operations can affect device stability. Use with caution and only on devices you own or manage.
            </div>

            <div class="card">
                <h3>🔧 System Operations</h3>

                <div class="card">
                    <h4>💻 Shell Commands</h4>
                    <div class="form-group">
                        <label for="shellCommand">Command:</label>
                        <input type="text" id="shellCommand" placeholder="ls -la /sdcard">
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="commandTimeout">Timeout (seconds):</label>
                            <input type="number" id="commandTimeout" value="30" min="5" max="300">
                        </div>
                        <button class="btn-primary" onclick="executeShellCommand()" disabled id="executeShellBtn">▶️ Execute</button>
                    </div>
                    <div id="shellOutput"></div>
                </div>

                <div class="card">
                    <h4>⚙️ System Settings</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="settingName">Setting Name:</label>
                            <select id="settingName">
                                <option value="screen_brightness">Screen Brightness</option>
                                <option value="screen_off_timeout">Screen Timeout</option>
                                <option value="stay_on_while_plugged_in">Stay Awake While Charging</option>
                                <option value="accelerometer_rotation">Auto Rotate</option>
                                <option value="window_animation_scale">Window Animation Scale</option>
                                <option value="transition_animation_scale">Transition Animation Scale</option>
                                <option value="animator_duration_scale">Animator Duration Scale</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="settingValue">Value:</label>
                            <input type="text" id="settingValue" placeholder="Enter value">
                        </div>
                        <button class="btn-info" onclick="getSystemSetting()" disabled id="getSettingBtn">📖 Get</button>
                        <button class="btn-warning" onclick="setSystemSetting()" disabled id="setSettingBtn">✏️ Set</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Files Tab -->
        <div id="files" class="tab-content">
            <div class="card">
                <h3>📁 File Management</h3>

                <div class="card">
                    <h4>⬇️ Download File from Device</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="deviceFilePath">Device File Path:</label>
                            <input type="text" id="deviceFilePath" placeholder="/sdcard/Download/file.txt">
                        </div>
                        <button class="btn-primary" onclick="downloadFile()" disabled id="downloadFileBtn">⬇️ Download</button>
                    </div>
                </div>

                <div class="card">
                    <h4>⬆️ Upload File to Device</h4>
                    <div class="file-upload" onclick="document.getElementById('uploadFile').click()">
                        <input type="file" id="uploadFile" style="display: none;" onchange="handleUploadFileSelect(this)">
                        <p>📄 Click to select file or drag and drop</p>
                    </div>
                    <div class="form-row" style="margin-top: 15px;">
                        <div class="form-group">
                            <label for="targetPath">Target Path on Device:</label>
                            <input type="text" id="targetPath" placeholder="/sdcard/Download/uploaded_file.txt">
                        </div>
                        <button class="btn-success" onclick="uploadFile()" disabled id="uploadFileBtn">⬆️ Upload</button>
                    </div>
                </div>

                <div id="fileResults"></div>
            </div>
        </div>

        <!-- Debug Tab -->
        <div id="debug" class="tab-content">
            <div class="card">
                <h3>🐛 Debug Information</h3>
                <button class="btn-info" onclick="getDebugInfo()">🔍 Get ADB Debug Info</button>
                <div id="debugOutput"></div>
            </div>

            <div class="card">
                <h3>📊 API Endpoints</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <strong>Device Management</strong>
                        GET /devices<br>
                        GET /devices/info<br>
                        GET /devices/storage<br>
                        GET /devices/network
                    </div>
                    <div class="info-item">
                        <strong>Process Management</strong>
                        GET /processes<br>
                        GET /processes/info<br>
                        POST /processes/stop<br>
                        POST /processes/kill
                    </div>
                    <div class="info-item">
                        <strong>Package Management</strong>
                        POST /upload<br>
                        GET /devices/packages<br>
                        POST /packages/download<br>
                        POST /packages/uninstall
                    </div>
                    <div class="info-item">
                        <strong>Wireless ADB</strong>
                        POST /wireless/enable<br>
                        POST /wireless/connect<br>
                        POST /wireless/pair<br>
                        GET /wireless/get-ip
                    </div>
                    <div class="info-item">
                        <strong>System Operations</strong>
                        POST /system/shell<br>
                        POST /system/file/download<br>
                        POST /system/file/upload<br>
                        GET /system/settings/get
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for detailed information -->
    <div id="infoModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Information</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div id="modalBody"></div>
        </div>
    </div>

    <script>
        // Global variables
        let currentDevice = null;
        let selectedFile = null;
        let uploadFile = null;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            refreshDevices();
            setupEventListeners();
        });

        function setupEventListeners() {
            document.getElementById('deviceSelect').addEventListener('change', onDeviceChange);
            document.getElementById('showAllProcesses').addEventListener('change', loadProcesses);

            // File upload drag and drop
            setupFileUpload();
        }

        function setupFileUpload() {
            const fileUpload = document.querySelector('.file-upload');

            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                fileUpload.addEventListener(eventName, preventDefaults, false);
            });

            ['dragenter', 'dragover'].forEach(eventName => {
                fileUpload.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                fileUpload.addEventListener(eventName, unhighlight, false);
            });

            fileUpload.addEventListener('drop', handleDrop, false);
        }

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function highlight(e) {
            e.currentTarget.classList.add('dragover');
        }

        function unhighlight(e) {
            e.currentTarget.classList.remove('dragover');
        }

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
                const file = files[0];
                if (file.name.endsWith('.apk')) {
                    document.getElementById('apkFile').files = files;
                    handleFileSelect(document.getElementById('apkFile'));
                } else {
                    showStatus('Please select an APK file', 'error');
                }
            }
        }

        // Tab management
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all nav tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // Device management
        async function refreshDevices() {
            try {
                const response = await fetch('/devices');
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to load devices');
                }

                const select = document.getElementById('deviceSelect');
                select.innerHTML = '<option value="">Select a device...</option>';

                data.devices.forEach(device => {
                    if (device.state === 'device') {
                        const option = document.createElement('option');
                        option.value = device.serial;
                        option.textContent = `${device.serial} (${device.model || 'Unknown'})`;
                        select.appendChild(option);
                    }
                });

                if (data.devices.filter(d => d.state === 'device').length === 0) {
                    select.innerHTML = '<option value="">No devices connected</option>';
                    showStatus('No devices connected. Please connect a device and enable USB debugging.', 'warning');
                }

            } catch (error) {
                showStatus(`Error loading devices: ${error.message}`, 'error');
            }
        }

        function onDeviceChange() {
            const select = document.getElementById('deviceSelect');
            currentDevice = select.value;

            // Enable/disable buttons based on device selection
            const buttons = [
                'deviceDetailsBtn', 'getInfoBtn', 'getStorageBtn', 'getNetworkBtn',
                'getPackagesBtn', 'loadProcessesBtn', 'enableWirelessBtn', 'getIPBtn',
                'executeShellBtn', 'getSettingBtn', 'setSettingBtn', 'downloadFileBtn'
            ];

            buttons.forEach(btnId => {
                const btn = document.getElementById(btnId);
                if (btn) btn.disabled = !currentDevice;
            });

            if (currentDevice) {
                showStatus(`Selected device: ${currentDevice}`, 'success');
                getDeviceInfo(); // Auto-load device info
            } else {
                document.getElementById('deviceOverview').innerHTML = '<div class="loading">Select a device to view overview</div>';
            }
        }

        // Status message display
        function showStatus(message, type = 'success') {
            const status = document.getElementById('deviceStatus');
            status.textContent = message;
            status.className = `status-message status-${type}`;
            status.style.display = 'block';

            setTimeout(() => {
                status.style.display = 'none';
            }, 5000);
        }

        // Modal management
        function showModal(title, content) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalBody').innerHTML = content;
            document.getElementById('infoModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('infoModal').style.display = 'none';
        }

        // Click outside modal to close
        window.onclick = function(event) {
            const modal = document.getElementById('infoModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // Device information functions
        async function getDeviceInfo() {
            if (!currentDevice) return;

            try {
                const response = await fetch(`/devices/info?serial=${encodeURIComponent(currentDevice)}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to get device info');
                }

                const overview = document.getElementById('deviceOverview');
                overview.innerHTML = `
                    <div class="info-grid">
                        <div class="info-item">
                            <strong>Device Model</strong>
                            ${data.model || 'Unknown'}
                        </div>
                        <div class="info-item">
                            <strong>Manufacturer</strong>
                            ${data.manufacturer || 'Unknown'}
                        </div>
                        <div class="info-item">
                            <strong>Android Version</strong>
                            ${data.android_version || 'Unknown'}
                        </div>
                        <div class="info-item">
                            <strong>API Level</strong>
                            ${data.api_level || 'Unknown'}
                        </div>
                        <div class="info-item">
                            <strong>CPU Architecture</strong>
                            ${data.cpu_abi || 'Unknown'}
                        </div>
                        <div class="info-item">
                            <strong>Hardware</strong>
                            ${data.hardware || 'Unknown'}
                        </div>
                    </div>
                `;

            } catch (error) {
                showStatus(`Error getting device info: ${error.message}`, 'error');
            }
        }

        async function showDeviceDetails() {
            if (!currentDevice) return;

            try {
                const response = await fetch(`/devices/info?serial=${encodeURIComponent(currentDevice)}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to get device details');
                }

                const details = Object.entries(data)
                    .map(([key, value]) => `<strong>${key.replace(/_/g, ' ').toUpperCase()}:</strong> ${value}`)
                    .join('<br>');

                showModal('Device Details', `<div style="line-height: 1.6;">${details}</div>`);

            } catch (error) {
                showStatus(`Error getting device details: ${error.message}`, 'error');
            }
        }

        async function getStorageInfo() {
            if (!currentDevice) return;

            try {
                const response = await fetch(`/devices/storage?serial=${encodeURIComponent(currentDevice)}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to get storage info');
                }

                const storage = data.storage;
                let content = '<h4>📊 Storage Information</h4>';

                if (storage.internal_total_kb) {
                    const totalGB = (storage.internal_total_kb / 1024 / 1024).toFixed(2);
                    const usedGB = (storage.internal_used_kb / 1024 / 1024).toFixed(2);
                    const freeGB = (storage.internal_available_kb / 1024 / 1024).toFixed(2);

                    content += `
                        <div class="info-grid">
                            <div class="info-item">
                                <strong>Total Internal Storage</strong>
                                ${totalGB} GB
                            </div>
                            <div class="info-item">
                                <strong>Used Space</strong>
                                ${usedGB} GB (${storage.internal_usage_percent}%)
                            </div>
                            <div class="info-item">
                                <strong>Available Space</strong>
                                ${freeGB} GB
                            </div>
                        </div>
                    `;
                }

                showModal('Storage Information', content);

            } catch (error) {
                showStatus(`Error getting storage info: ${error.message}`, 'error');
            }
        }

        async function getNetworkInfo() {
            if (!currentDevice) return;

            try {
                const response = await fetch(`/devices/network?serial=${encodeURIComponent(currentDevice)}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to get network info');
                }

                const network = data.network;
                const content = `
                    <h4>🌐 Network Information</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <strong>WiFi Status</strong>
                            ${network.wifi_enabled ? '✅ Enabled' : '❌ Disabled'}
                        </div>
                        <div class="info-item">
                            <strong>WiFi Connected</strong>
                            ${network.wifi_connected ? '✅ Connected' : '❌ Not Connected'}
                        </div>
                        <div class="info-item">
                            <strong>IP Address</strong>
                            ${network.ip_address || 'Not available'}
                        </div>
                        <div class="info-item">
                            <strong>Mobile Data</strong>
                            ${network.mobile_data_connected ? '✅ Connected' : '❌ Not Connected'}
                        </div>
                    </div>
                `;

                showModal('Network Information', content);

            } catch (error) {
                showStatus(`Error getting network info: ${error.message}`, 'error');
            }
        }

        async function getInstalledPackages() {
            if (!currentDevice) return;

            try {
                const response = await fetch(`/devices/packages?serial=${encodeURIComponent(currentDevice)}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to get packages');
                }

                const packages = data.packages;
                let content = `<h4>📦 Installed Packages (${packages.length})</h4>`;
                content += '<div style="max-height: 400px; overflow-y: auto;">';

                packages.forEach(pkg => {
                    content += `
                        <div class="info-item" style="margin-bottom: 10px;">
                            <strong>${pkg.name}</strong>
                            ${pkg.version ? `<br>Version: ${pkg.version}` : ''}
                        </div>
                    `;
                });

                content += '</div>';
                showModal('Installed Packages', content);

            } catch (error) {
                showStatus(`Error getting packages: ${error.message}`, 'error');
            }
        }

        // Process management functions
        async function loadProcesses() {
            if (!currentDevice) return;

            const container = document.getElementById('processContainer');
            container.innerHTML = '<div class="loading">Loading processes...</div>';

            try {
                const showAll = document.getElementById('showAllProcesses').checked;
                const response = await fetch(`/processes?serial=${encodeURIComponent(currentDevice)}&show_all=${showAll}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to load processes');
                }

                const processes = data.processes;

                if (processes.length === 0) {
                    container.innerHTML = '<div class="loading">No processes found</div>';
                    return;
                }

                let tableHTML = `
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>Package Name</th>
                                    <th>PID</th>
                                    <th>User</th>
                                    <th>Process Name</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                processes.forEach(process => {
                    tableHTML += `
                        <tr>
                            <td>${process.package_name || 'N/A'}</td>
                            <td>${process.pid}</td>
                            <td>${process.user}</td>
                            <td>${process.name}</td>
                            <td>
                                <div class="action-buttons">
                                    ${process.package_name ? `
                                        <button class="btn-info" onclick="getProcessInfo('${process.package_name}')">ℹ️ Info</button>
                                        <button class="btn-danger" onclick="stopApplication('${process.package_name}')">⏹️ Stop</button>
                                    ` : ''}
                                    <button class="btn-danger" onclick="killProcess(${process.pid})">❌ Kill</button>
                                </div>
                            </td>
                        </tr>
                    `;
                });

                tableHTML += '</tbody></table></div>';
                container.innerHTML = tableHTML;

            } catch (error) {
                container.innerHTML = `<div class="loading">Error: ${error.message}</div>`;
                showStatus(`Error loading processes: ${error.message}`, 'error');
            }
        }

        async function getProcessInfo(packageName) {
            try {
                const response = await fetch(`/processes/info?serial=${encodeURIComponent(currentDevice)}&package_name=${encodeURIComponent(packageName)}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to get process info');
                }

                let content = `
                    <h4>⚙️ Process Information</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <strong>Package Name</strong>
                            ${data.package_name}
                        </div>
                        <div class="info-item">
                            <strong>Process ID</strong>
                            ${data.pid}
                        </div>
                `;

                if (data.memory_info) {
                    if (data.memory_info.pss_kb) {
                        content += `
                            <div class="info-item">
                                <strong>Memory (PSS)</strong>
                                ${(data.memory_info.pss_kb / 1024).toFixed(1)} MB
                            </div>
                        `;
                    }
                    if (data.memory_info.ram_kb) {
                        content += `
                            <div class="info-item">
                                <strong>RAM Usage</strong>
                                ${(data.memory_info.ram_kb / 1024).toFixed(1)} MB
                            </div>
                        `;
                    }
                }

                content += '</div>';
                showModal('Process Information', content);

            } catch (error) {
                showStatus(`Error getting process info: ${error.message}`, 'error');
            }
        }

        async function stopApplication(packageName) {
            if (!confirm(`Are you sure you want to stop ${packageName}?`)) {
                return;
            }

            try {
                const response = await fetch('/processes/stop', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        serial: currentDevice,
                        package_name: packageName
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to stop application');
                }

                showStatus(`Successfully stopped ${packageName}`, 'success');
                loadProcesses(); // Refresh the process list

            } catch (error) {
                showStatus(`Error stopping application: ${error.message}`, 'error');
            }
        }

        async function killProcess(pid) {
            if (!confirm(`Are you sure you want to kill process ${pid}?`)) {
                return;
            }

            try {
                const response = await fetch('/processes/kill', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        serial: currentDevice,
                        pid: pid
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to kill process');
                }

                showStatus(`Successfully killed process ${pid}`, 'success');
                loadProcesses(); // Refresh the process list

            } catch (error) {
                showStatus(`Error killing process: ${error.message}`, 'error');
            }
        }

        // Package management functions
        function handleFileSelect(input) {
            if (input.files && input.files[0]) {
                selectedFile = input.files[0];
                document.getElementById('installBtn').disabled = false;
                showStatus(`Selected: ${selectedFile.name}`, 'info');
            }
        }

        function handleUploadFileSelect(input) {
            if (input.files && input.files[0]) {
                uploadFile = input.files[0];
                document.getElementById('uploadFileBtn').disabled = false;
                showStatus(`Selected: ${uploadFile.name}`, 'info');
            }
        }

        async function installAPK() {
            if (!selectedFile || !currentDevice) return;

            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('serial', currentDevice);
            formData.append('grant_permissions', document.getElementById('grantPermissions').checked);
            formData.append('replace', document.getElementById('replaceExisting').checked);

            try {
                document.getElementById('uploadProgress').style.display = 'block';
                document.getElementById('uploadStatus').textContent = 'Installing APK...';

                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to install APK');
                }

                showStatus(`Successfully installed ${selectedFile.name}`, 'success');
                selectedFile = null;
                document.getElementById('installBtn').disabled = true;
                document.getElementById('apkFile').value = '';

            } catch (error) {
                showStatus(`Error installing APK: ${error.message}`, 'error');
            } finally {
                document.getElementById('uploadProgress').style.display = 'none';
            }
        }

        // Wireless ADB functions
        async function enableWireless() {
            if (!currentDevice) return;

            const port = document.getElementById('wirelessPort').value;

            try {
                const response = await fetch('/wireless/enable', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        serial: currentDevice,
                        port: parseInt(port)
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to enable wireless ADB');
                }

                showStatus(data.message, 'success');

            } catch (error) {
                showStatus(`Error enabling wireless ADB: ${error.message}`, 'error');
            }
        }

        async function getDeviceIP() {
            if (!currentDevice) return;

            try {
                const response = await fetch(`/wireless/get-ip?serial=${encodeURIComponent(currentDevice)}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to get device IP');
                }

                document.getElementById('deviceIP').value = data.ip_address;
                showStatus(`Device IP: ${data.ip_address}`, 'success');

            } catch (error) {
                showStatus(`Error getting device IP: ${error.message}`, 'error');
            }
        }

        async function connectWireless() {
            const ip = document.getElementById('deviceIP').value.trim();
            const port = document.getElementById('connectPort').value;

            if (!ip) {
                showStatus('Please enter device IP address', 'error');
                return;
            }

            try {
                const response = await fetch('/wireless/connect', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        ip_address: ip,
                        port: parseInt(port)
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to connect wirelessly');
                }

                showStatus(data.message, 'success');
                refreshDevices(); // Refresh device list

            } catch (error) {
                showStatus(`Error connecting wirelessly: ${error.message}`, 'error');
            }
        }

        async function disconnectWireless() {
            const ip = document.getElementById('deviceIP').value.trim();
            const port = document.getElementById('connectPort').value;

            if (!ip) {
                showStatus('Please enter device IP address', 'error');
                return;
            }

            try {
                const response = await fetch('/wireless/disconnect', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        ip_address: ip,
                        port: parseInt(port)
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to disconnect');
                }

                showStatus(data.message, 'success');
                refreshDevices(); // Refresh device list

            } catch (error) {
                showStatus(`Error disconnecting: ${error.message}`, 'error');
            }
        }

        async function pairDevice() {
            const ip = document.getElementById('pairIP').value.trim();
            const port = document.getElementById('pairPort').value;
            const code = document.getElementById('pairingCode').value.trim();

            if (!ip || !port || !code) {
                showStatus('Please fill in all pairing fields', 'error');
                return;
            }

            try {
                const response = await fetch('/wireless/pair', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        ip_address: ip,
                        port: parseInt(port),
                        pairing_code: code
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to pair device');
                }

                showStatus(data.message, 'success');

            } catch (error) {
                showStatus(`Error pairing device: ${error.message}`, 'error');
            }
        }

        // SIM Management functions
        async function getSimInfo() {
            if (!currentDevice) return;

            try {
                const response = await fetch(`/sim/info?serial=${encodeURIComponent(currentDevice)}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to get SIM info');
                }

                const simInfo = data.sim_info;
                let content = '<h4>📱 SIM Card Information</h4><div class="info-grid">';

                // Display SIM states
                if (simInfo.sim_states) {
                    content += `
                        <div class="info-item" style="grid-column: 1 / -1;">
                            <strong>SIM States</strong>
                            ${simInfo.sim_states.map(state => `<div>${state}</div>`).join('')}
                        </div>
                    `;
                }

                // Display SIM operators and states
                for (let i = 0; i < 2; i++) {
                    const operator = simInfo[`sim_${i}_operator`];
                    const state = simInfo[`sim_${i}_state`];

                    if (operator || state) {
                        content += `
                            <div class="info-item">
                                <strong>SIM ${i + 1}</strong>
                                ${operator ? `Operator: ${operator}<br>` : ''}
                                ${state ? `State: ${state}` : 'No SIM detected'}
                            </div>
                        `;
                    }
                }

                content += '</div>';

                // Add SIM control buttons
                content += `
                    <div style="margin-top: 20px;">
                        <h4>🔧 SIM Control</h4>
                        <div class="form-row">
                            <button class="btn-success" onclick="toggleSIM(0, true)">📶 Enable SIM 1</button>
                            <button class="btn-danger" onclick="toggleSIM(0, false)">📵 Disable SIM 1</button>
                            <button class="btn-success" onclick="toggleSIM(1, true)">📶 Enable SIM 2</button>
                            <button class="btn-danger" onclick="toggleSIM(1, false)">📵 Disable SIM 2</button>
                        </div>
                        <div class="form-row" style="margin-top: 15px;">
                            <button class="btn-info" onclick="setPreferredSIM(0, 'data')">📊 Set SIM 1 for Data</button>
                            <button class="btn-info" onclick="setPreferredSIM(1, 'data')">📊 Set SIM 2 for Data</button>
                            <button class="btn-warning" onclick="setPreferredSIM(0, 'voice')">📞 Set SIM 1 for Calls</button>
                            <button class="btn-warning" onclick="setPreferredSIM(1, 'voice')">📞 Set SIM 2 for Calls</button>
                        </div>
                    </div>
                `;

                showModal('SIM Management', content);

            } catch (error) {
                showStatus(`Error getting SIM info: ${error.message}`, 'error');
            }
        }

        async function toggleSIM(simSlot, enable) {
            if (!currentDevice) return;

            const action = enable ? 'enable' : 'disable';

            if (!confirm(`Are you sure you want to ${action} SIM ${simSlot + 1}?`)) {
                return;
            }

            try {
                const response = await fetch('/sim/toggle', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        serial: currentDevice,
                        sim_slot: simSlot,
                        enable: enable
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || `Failed to ${action} SIM`);
                }

                showStatus(data.message, 'success');

            } catch (error) {
                showStatus(`Error ${action}ing SIM: ${error.message}`, 'error');
            }
        }

        async function setPreferredSIM(simSlot, serviceType) {
            if (!currentDevice) return;

            try {
                const response = await fetch('/sim/set-preferred', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        serial: currentDevice,
                        sim_slot: simSlot,
                        service_type: serviceType
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Failed to set preferred SIM');
                }

                showStatus(data.message, 'success');

            } catch (error) {
                showStatus(`Error setting preferred SIM: ${error.message}`, 'error');
            }
        }

        // Debug functions
        async function getDebugInfo() {
            try {
                const response = await fetch('/debug/adb');
                const data = await response.json();

                const output = document.getElementById('debugOutput');
                output.innerHTML = `
                    <div class="card" style="margin-top: 20px;">
                        <h4>🔍 ADB Debug Information</h4>
                        <pre style="background: #f8f9fa; padding: 15px; border-radius: 6px; overflow-x: auto;">${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;

            } catch (error) {
                showStatus(`Error getting debug info: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
"""


@app.route('/process-manager')
def process_manager():
    """Serve the process management web interface."""
    return render_template_string(COMPREHENSIVE_UI_HTML)


@app.route('/')
def index():
    """Serve a simple index page with links to available features."""
    index_html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Android ADB Manager</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                padding: 40px;
            }
            h1 {
                color: #333;
                text-align: center;
                margin-bottom: 30px;
            }
            .feature-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-top: 30px;
            }
            .feature-card {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 20px;
                text-decoration: none;
                color: inherit;
                transition: transform 0.2s, box-shadow 0.2s;
            }
            .feature-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                text-decoration: none;
                color: inherit;
            }
            .feature-card h3 {
                margin-top: 0;
                color: #007bff;
            }
            .api-section {
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
            }
            .api-endpoint {
                background: #f8f9fa;
                padding: 10px;
                border-radius: 4px;
                margin: 10px 0;
                font-family: monospace;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Android ADB Manager</h1>
            <p>Manage your Android devices and applications through ADB (Android Debug Bridge).</p>

            <div class="feature-grid">
                <a href="/process-manager" class="feature-card">
                    <h3>Process Manager</h3>
                    <p>View and manage running Android applications and processes. Stop applications, kill processes, and monitor memory usage.</p>
                </a>

                <div class="feature-card">
                    <h3>APK Installation</h3>
                    <p>Upload and install APK files to connected Android devices via the REST API.</p>
                    <p><strong>Endpoint:</strong> POST /upload</p>
                </div>
            </div>

            <div class="api-section">
                <h2>API Endpoints</h2>
                <p>This server provides REST API endpoints for programmatic access:</p>

                <div class="api-endpoint">GET /devices - List connected devices</div>
                <div class="api-endpoint">GET /processes - List running processes</div>
                <div class="api-endpoint">GET /processes/info - Get process details</div>
                <div class="api-endpoint">POST /processes/stop - Stop an application</div>
                <div class="api-endpoint">POST /processes/kill - Kill a process by PID</div>
                <div class="api-endpoint">POST /upload - Upload and install APK</div>
                <div class="api-endpoint">GET /health - Health check</div>
            </div>
        </div>
    </body>
    </html>
    """
    return render_template_string(index_html)


# Advanced Samsung Device Management API Routes

@app.route('/api/samsung/mtp/activate/<serial>', methods=['POST'])
@auth.login_required
@limiter.limit("5 per minute")
@handle_adb_errors
def activate_mtp_locked_device(serial):
    """Activate MTP on a locked Samsung device using advanced driver capabilities."""
    try:
        samsung_manager = SamsungDeviceManager(ADB_PATH)
        success, message = samsung_manager.activate_mtp_locked_device(serial)

        return jsonify({
            'success': success,
            'message': message,
            'serial': serial,
            'feature': 'MTP Activation on Locked Device'
        })

    except Exception as e:
        logger.error(f"Error activating MTP on locked device: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/samsung/frp/bypass/<serial>', methods=['POST'])
@auth.login_required
@limiter.limit("2 per minute")
@handle_adb_errors
def bypass_frp_protection(serial):
    """Bypass FRP (Factory Reset Protection) - Use only on authorized devices."""
    try:
        # Security warning
        logger.warning(f"FRP bypass attempted on device {serial} by user {auth.current_user()}")

        samsung_manager = SamsungDeviceManager(ADB_PATH)
        success, message = samsung_manager.bypass_frp(serial)

        return jsonify({
            'success': success,
            'message': message,
            'serial': serial,
            'feature': 'FRP Bypass',
            'warning': 'Use only on authorized devices'
        })

    except Exception as e:
        logger.error(f"Error bypassing FRP: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/samsung/mdm/<action>/<serial>', methods=['POST'])
@auth.login_required
@limiter.limit("5 per minute")
@handle_adb_errors
def manage_mdm(action, serial):
    """Manage MDM (Mobile Device Management) on Samsung devices."""
    try:
        valid_actions = ['disable', 'enable', 'bypass', 'status']
        if action not in valid_actions:
            return jsonify({'error': f'Invalid action. Must be one of: {valid_actions}'}), 400

        samsung_manager = SamsungDeviceManager(ADB_PATH)
        success, result = samsung_manager.manage_mdm(serial, action)

        return jsonify({
            'success': success,
            'result': result,
            'serial': serial,
            'action': action,
            'feature': 'MDM Management'
        })

    except Exception as e:
        logger.error(f"Error managing MDM: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/advanced/screen-control/<action>/<serial>', methods=['POST'])
@auth.login_required
@limiter.limit("10 per minute")
@handle_adb_errors
def remote_screen_control(action, serial):
    """Advanced remote screen control with scrcpy integration."""
    try:
        valid_actions = ['start', 'stop', 'screenshot', 'record']
        if action not in valid_actions:
            return jsonify({'error': f'Invalid action. Must be one of: {valid_actions}'}), 400

        controller = AdvancedDeviceController(ADB_PATH)
        success, message = controller.remote_screen_control(serial, action)

        return jsonify({
            'success': success,
            'message': message,
            'serial': serial,
            'action': action,
            'feature': 'Remote Screen Control'
        })

    except Exception as e:
        logger.error(f"Error with screen control: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/advanced/device-forensics/<serial>', methods=['GET'])
@auth.login_required
@limiter.limit("3 per minute")
@handle_adb_errors
def device_forensics(serial):
    """Advanced device forensics and data extraction."""
    try:
        forensics_data = {
            'serial': serial,
            'timestamp': datetime.now().isoformat(),
            'device_info': {},
            'security_analysis': {},
            'installed_apps': [],
            'system_logs': [],
            'network_connections': [],
            'file_system_analysis': {}
        }

        # Get comprehensive device information
        device_info, _ = get_comprehensive_device_info(serial)
        if device_info:
            forensics_data['device_info'] = device_info

        # Security analysis
        security_analysis = perform_security_analysis(serial)
        forensics_data['security_analysis'] = security_analysis

        # Get installed applications with detailed info
        apps, _ = get_installed_packages(serial, include_system=True)
        if apps:
            forensics_data['installed_apps'] = apps[:100]  # Limit to first 100

        # Get system logs
        logs = extract_system_logs(serial)
        forensics_data['system_logs'] = logs

        # Network analysis
        network_info, _ = get_device_network_info(serial)
        if network_info:
            forensics_data['network_connections'] = network_info

        return jsonify({
            'success': True,
            'forensics_data': forensics_data,
            'feature': 'Device Forensics'
        })

    except Exception as e:
        logger.error(f"Error in device forensics: {str(e)}")
        return jsonify({'error': str(e)}), 500

def perform_security_analysis(serial):
    """Perform comprehensive security analysis of the device."""
    try:
        analysis = {
            'adb_enabled': False,
            'developer_options': False,
            'unknown_sources': False,
            'encryption_status': 'unknown',
            'screen_lock': 'unknown',
            'root_status': 'unknown',
            'security_patches': [],
            'suspicious_apps': [],
            'permissions_analysis': {}
        }

        # Check ADB status
        code, out, _ = run_adb_command(['-s', serial, 'shell', 'settings', 'get', 'global', 'adb_enabled'])
        if code == 0:
            analysis['adb_enabled'] = out.strip() == '1'

        # Check developer options
        code, out, _ = run_adb_command(['-s', serial, 'shell', 'settings', 'get', 'global', 'development_settings_enabled'])
        if code == 0:
            analysis['developer_options'] = out.strip() == '1'

        # Check unknown sources
        code, out, _ = run_adb_command(['-s', serial, 'shell', 'settings', 'get', 'secure', 'install_non_market_apps'])
        if code == 0:
            analysis['unknown_sources'] = out.strip() == '1'

        # Check encryption status
        code, out, _ = run_adb_command(['-s', serial, 'shell', 'getprop', 'ro.crypto.state'])
        if code == 0:
            analysis['encryption_status'] = out.strip()

        # Check for root
        code, out, _ = run_adb_command(['-s', serial, 'shell', 'which', 'su'])
        if code == 0 and out.strip():
            analysis['root_status'] = 'rooted'
        else:
            analysis['root_status'] = 'not_rooted'

        return analysis

    except Exception as e:
        logger.error(f"Security analysis error: {str(e)}")
        return {'error': str(e)}

def extract_system_logs(serial):
    """Extract system logs for forensic analysis."""
    try:
        logs = []

        # Get logcat output (last 100 lines)
        code, out, _ = run_adb_command(['-s', serial, 'shell', 'logcat', '-d', '-t', '100'])
        if code == 0:
            logs.append({
                'type': 'logcat',
                'content': out.splitlines()[-50:]  # Last 50 lines
            })

        # Get dmesg output
        code, out, _ = run_adb_command(['-s', serial, 'shell', 'dmesg'])
        if code == 0:
            logs.append({
                'type': 'dmesg',
                'content': out.splitlines()[-30:]  # Last 30 lines
            })

        return logs

    except Exception as e:
        logger.error(f"Log extraction error: {str(e)}")
        return []

if __name__ == '__main__':
    # For development only. Use a production server (e.g., gunicorn) in production.
    app.run(host='0.0.0.0', port=5000, debug=True)