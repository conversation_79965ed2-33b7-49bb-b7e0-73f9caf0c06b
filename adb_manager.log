2025-08-27 11:03:06,094 - __main__ - INFO - Starting Android ADB Manager
2025-08-27 11:03:06,094 - __main__ - INFO - ADB Path: adb (from PATH)
2025-08-27 11:03:06,094 - __main__ - INFO - Upload Directory: /workspaces/Break/uploads
2025-08-27 11:12:08,122 - __main__ - INFO - Starting Android ADB Manager
2025-08-27 11:12:08,122 - __main__ - INFO - ADB Path: adb (from PATH)
2025-08-27 11:12:08,122 - __main__ - INFO - Upload Directory: /workspaces/Break/uploads
2025-08-27 11:17:10,874 - __main__ - INFO - Starting Android ADB Manager
2025-08-27 11:17:10,875 - __main__ - INFO - ADB Path: adb (from PATH)
2025-08-27 11:17:10,875 - __main__ - INFO - Upload Directory: /workspaces/Break/uploads
2025-08-27 11:17:10,899 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
2025-08-27 11:17:10,900 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 11:17:10,900 - werkzeug - INFO -  * Restarting with stat
2025-08-27 11:17:11,370 - __main__ - INFO - Starting Android ADB Manager
2025-08-27 11:17:11,371 - __main__ - INFO - ADB Path: adb (from PATH)
2025-08-27 11:17:11,371 - __main__ - INFO - Upload Directory: /workspaces/Break/uploads
2025-08-27 11:17:11,390 - werkzeug - WARNING -  * Debugger is active!
2025-08-27 11:17:11,399 - werkzeug - INFO -  * Debugger PIN: 274-156-101
2025-08-27 11:17:16,426 - werkzeug - INFO -  * Detected change in '/workspaces/Break/app.py', reloading
2025-08-27 11:17:16,475 - werkzeug - INFO -  * Restarting with stat
2025-08-27 11:17:17,016 - __main__ - INFO - Starting Android ADB Manager
2025-08-27 11:17:17,017 - __main__ - INFO - ADB Path: adb (from PATH)
2025-08-27 11:17:17,017 - __main__ - INFO - Upload Directory: /workspaces/Break/uploads
2025-08-27 11:17:17,050 - werkzeug - WARNING -  * Debugger is active!
2025-08-27 11:17:17,050 - werkzeug - INFO -  * Debugger PIN: 274-156-101
2025-08-29 03:49:55,775 - __main__ - INFO - Starting Android ADB Manager
2025-08-29 03:49:55,799 - __main__ - INFO - ADB Path: C:\platform-tools\adb.exe
2025-08-29 03:49:55,917 - __main__ - INFO - Upload Directory: C:\Users\<USER>\Documents\augment-projects\Break\Break\uploads
2025-08-29 03:49:56,513 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-08-29 03:49:56,516 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-29 03:49:56,522 - werkzeug - INFO -  * Restarting with stat
2025-08-29 03:50:06,872 - __main__ - INFO - Starting Android ADB Manager
2025-08-29 03:50:06,873 - __main__ - INFO - ADB Path: C:\platform-tools\adb.exe
2025-08-29 03:50:06,877 - __main__ - INFO - Upload Directory: C:\Users\<USER>\Documents\augment-projects\Break\Break\uploads
2025-08-29 03:50:07,125 - werkzeug - WARNING -  * Debugger is active!
2025-08-29 03:50:07,142 - werkzeug - INFO -  * Debugger PIN: 141-054-317
2025-08-29 03:50:07,720 - werkzeug - INFO - 127.0.0.1 - - [29/Aug/2025 03:50:07] "GET /?ide_webview_request_time=1756432202109 HTTP/1.1" 200 -
2025-08-29 03:50:10,369 - werkzeug - INFO - 127.0.0.1 - - [29/Aug/2025 03:50:10] "[33mGET /@vite/client HTTP/1.1[0m" 404 -
2025-08-29 03:52:02,625 - __main__ - INFO - Starting Android ADB Manager
2025-08-29 03:52:02,626 - __main__ - INFO - ADB Path: C:\platform-tools\adb.exe
2025-08-29 03:52:02,627 - __main__ - INFO - Upload Directory: C:\Users\<USER>\Documents\augment-projects\Break\Break\uploads
2025-08-29 03:52:02,956 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-08-29 03:52:02,960 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-29 03:52:02,965 - werkzeug - INFO -  * Restarting with stat
2025-08-29 03:52:06,523 - __main__ - INFO - Starting Android ADB Manager
2025-08-29 03:52:06,525 - __main__ - INFO - ADB Path: C:\platform-tools\adb.exe
2025-08-29 03:52:06,525 - __main__ - INFO - Upload Directory: C:\Users\<USER>\Documents\augment-projects\Break\Break\uploads
2025-08-29 03:52:06,694 - werkzeug - WARNING -  * Debugger is active!
2025-08-29 03:52:06,719 - werkzeug - INFO -  * Debugger PIN: 141-054-317
2025-08-29 03:53:14,333 - __main__ - INFO - Executing ADB command: C:\platform-tools\adb.exe devices
2025-08-29 03:53:22,923 - __main__ - INFO - ADB command completed with return code: 0
2025-08-29 03:53:22,924 - __main__ - INFO - Found 0 connected devices
2025-08-29 03:53:22,925 - werkzeug - INFO - 127.0.0.1 - - [29/Aug/2025 03:53:22] "[31m[1mGET /processes?ide_webview_request_time=1756432393629 HTTP/1.1[0m" 400 -
2025-08-29 03:53:24,436 - werkzeug - INFO - 127.0.0.1 - - [29/Aug/2025 03:53:24] "[33mGET /@vite/client HTTP/1.1[0m" 404 -
2025-08-29 03:54:20,077 - __main__ - INFO - Executing ADB command: C:\platform-tools\adb.exe devices
2025-08-29 03:54:20,237 - __main__ - INFO - ADB command completed with return code: 0
2025-08-29 03:54:20,245 - __main__ - INFO - Found 0 connected devices
2025-08-29 03:54:20,248 - werkzeug - INFO - 127.0.0.1 - - [29/Aug/2025 03:54:20] "[31m[1mGET /processes?ide_webview_request_time=1756432393629 HTTP/1.1[0m" 400 -
2025-08-29 03:54:20,408 - werkzeug - INFO - 127.0.0.1 - - [29/Aug/2025 03:54:20] "[33mGET /@vite/client HTTP/1.1[0m" 404 -
2025-08-29 03:54:24,499 - werkzeug - INFO - 127.0.0.1 - - [29/Aug/2025 03:54:24] "[33mGET /@vite/client HTTP/1.1[0m" 404 -
2025-08-29 03:55:20,589 - werkzeug - INFO - 127.0.0.1 - - [29/Aug/2025 03:55:20] "GET /process-manager HTTP/1.1" 200 -
2025-08-29 03:55:21,310 - werkzeug - INFO - 127.0.0.1 - - [29/Aug/2025 03:55:21] "[33mGET /@vite/client HTTP/1.1[0m" 404 -
2025-08-29 03:55:21,326 - __main__ - INFO - Executing ADB command: C:\platform-tools\adb.exe devices
2025-08-29 03:55:21,537 - __main__ - INFO - ADB command completed with return code: 0
2025-08-29 03:55:21,537 - __main__ - INFO - Found 0 connected devices
2025-08-29 03:55:21,541 - werkzeug - INFO - 127.0.0.1 - - [29/Aug/2025 03:55:21] "GET /devices HTTP/1.1" 200 -
2025-08-29 03:55:37,254 - __main__ - INFO - Executing ADB command: C:\platform-tools\adb.exe devices
2025-08-29 03:55:37,367 - __main__ - INFO - ADB command completed with return code: 0
2025-08-29 03:55:37,368 - __main__ - INFO - Found 0 connected devices
2025-08-29 03:55:37,371 - werkzeug - INFO - 127.0.0.1 - - [29/Aug/2025 03:55:37] "GET /devices HTTP/1.1" 200 -
2025-08-29 04:21:02,716 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\augment-projects\\Break\\Break\\app.py', reloading
2025-08-29 04:21:05,901 - werkzeug - INFO -  * Restarting with stat
2025-09-19 16:13:01,253 - __main__ - INFO - Starting Android ADB Manager
2025-09-19 16:13:01,399 - __main__ - INFO - ADB Path: C:\platform-tools\adb.exe
2025-09-19 16:13:01,401 - __main__ - INFO - Upload Directory: C:\Users\<USER>\Documents\augment-projects\Break\Break\uploads
2025-09-19 16:13:03,388 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-09-19 16:13:03,433 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-09-19 16:13:03,501 - werkzeug - INFO -  * Restarting with stat
2025-09-19 16:13:15,873 - __main__ - INFO - Starting Android ADB Manager
2025-09-19 16:13:15,879 - __main__ - INFO - ADB Path: C:\platform-tools\adb.exe
2025-09-19 16:13:15,983 - __main__ - INFO - Upload Directory: C:\Users\<USER>\Documents\augment-projects\Break\Break\uploads
2025-09-19 16:13:16,238 - werkzeug - WARNING -  * Debugger is active!
2025-09-19 16:13:16,553 - werkzeug - INFO -  * Debugger PIN: 247-365-591
2025-09-19 16:20:23,485 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 16:20:23] "GET /?ide_webview_request_time=1758291619900 HTTP/1.1" 200 -
2025-09-19 16:20:26,234 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 16:20:26] "[33mGET /@vite/client HTTP/1.1[0m" 404 -
2025-09-19 16:20:50,963 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 16:20:50] "GET /process-manager HTTP/1.1" 200 -
2025-09-19 16:20:52,824 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 16:20:52] "[33mGET /@vite/client HTTP/1.1[0m" 404 -
2025-09-19 16:20:52,833 - __main__ - INFO - Executing ADB command: C:\platform-tools\adb.exe devices
2025-09-19 16:21:19,118 - __main__ - INFO - ADB command completed with return code: 0
2025-09-19 16:21:19,120 - __main__ - INFO - Found 0 connected devices
2025-09-19 16:21:19,200 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 16:21:19] "GET /devices HTTP/1.1" 200 -
2025-09-19 16:21:53,647 - __main__ - INFO - Executing ADB command: C:\platform-tools\adb.exe devices
2025-09-19 16:21:54,722 - __main__ - INFO - ADB command completed with return code: 0
2025-09-19 16:21:54,793 - __main__ - INFO - Found 0 connected devices
2025-09-19 16:21:54,859 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 16:21:54] "GET /devices HTTP/1.1" 200 -
2025-09-19 16:27:03,783 - __main__ - INFO - Executing ADB command: C:\platform-tools\adb.exe devices
2025-09-19 16:27:04,244 - __main__ - INFO - ADB command completed with return code: 0
2025-09-19 16:27:04,287 - __main__ - INFO - Found 0 connected devices
2025-09-19 16:27:04,453 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 16:27:04] "GET /devices HTTP/1.1" 200 -
2025-09-19 18:48:58,158 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\augment-projects\\Break\\Break\\app.py', reloading
2025-09-19 18:49:34,465 - werkzeug - INFO -  * Restarting with stat
2025-09-19 18:50:43,490 - __main__ - INFO - Starting Android ADB Manager
2025-09-19 18:50:43,855 - __main__ - INFO - ADB Path: C:\platform-tools\adb.exe
2025-09-19 18:50:43,856 - __main__ - INFO - Upload Directory: C:\Users\<USER>\Documents\augment-projects\Break\Break\uploads
2025-09-19 18:50:55,116 - werkzeug - WARNING -  * Debugger is active!
2025-09-19 18:50:55,158 - werkzeug - INFO -  * Debugger PIN: 247-365-591
2025-09-19 18:51:55,358 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\augment-projects\\Break\\Break\\usb_manager.py', reloading
2025-09-19 18:51:58,132 - werkzeug - INFO -  * Restarting with stat
2025-09-19 18:52:28,290 - __main__ - INFO - Starting Android ADB Manager
2025-09-19 18:52:28,412 - __main__ - INFO - ADB Path: C:\platform-tools\adb.exe
2025-09-19 18:52:28,611 - __main__ - INFO - Upload Directory: C:\Users\<USER>\Documents\augment-projects\Break\Break\uploads
2025-09-19 18:52:28,914 - werkzeug - WARNING -  * Debugger is active!
2025-09-19 18:52:29,012 - werkzeug - INFO -  * Debugger PIN: 247-365-591
2025-09-19 18:54:37,480 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 18:54:37] "GET /process-manager HTTP/1.1" 200 -
2025-09-19 18:54:47,968 - __main__ - INFO - Executing ADB command: C:\platform-tools\adb.exe devices
2025-09-19 18:54:58,061 - __main__ - INFO - ADB command completed with return code: 0
2025-09-19 18:54:58,062 - __main__ - INFO - Found 0 connected devices
2025-09-19 18:54:58,063 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 18:54:58] "GET /devices HTTP/1.1" 200 -
2025-09-19 18:55:40,337 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 18:55:40] "GET /process-manager HTTP/1.1" 200 -
2025-09-19 18:55:40,359 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 18:55:40] "GET /process-manager HTTP/1.1" 200 -
2025-09-19 19:02:43,843 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 19:02:43] "GET /process-manager HTTP/1.1" 200 -
2025-09-19 19:02:45,407 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 19:02:45] "GET /process-manager HTTP/1.1" 200 -
2025-09-19 19:02:52,785 - __main__ - INFO - Executing ADB command: C:\platform-tools\adb.exe devices
2025-09-19 19:02:53,671 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 19:02:53] "[33mGET /@vite/client HTTP/1.1[0m" 404 -
2025-09-19 19:02:58,788 - __main__ - INFO - ADB command completed with return code: 0
2025-09-19 19:02:58,789 - __main__ - INFO - Found 0 connected devices
2025-09-19 19:02:58,790 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 19:02:58] "GET /devices HTTP/1.1" 200 -
2025-09-19 21:36:02,595 - __main__ - INFO - Starting Android ADB Manager
2025-09-19 21:36:02,682 - __main__ - INFO - ADB Path: C:\platform-tools\adb.exe
2025-09-19 21:36:02,697 - __main__ - INFO - Upload Directory: C:\Users\<USER>\Documents\augment-projects\Break\break\uploads
2025-09-19 21:36:06,745 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-09-19 21:36:06,757 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-09-19 21:36:06,848 - werkzeug - INFO -  * Restarting with stat
2025-09-19 21:36:16,897 - __main__ - INFO - Starting Android ADB Manager
2025-09-19 21:36:16,899 - __main__ - INFO - ADB Path: C:\platform-tools\adb.exe
2025-09-19 21:36:16,910 - __main__ - INFO - Upload Directory: C:\Users\<USER>\Documents\augment-projects\Break\break\uploads
2025-09-19 21:36:18,185 - werkzeug - WARNING -  * Debugger is active!
2025-09-19 21:36:18,416 - werkzeug - INFO -  * Debugger PIN: 247-365-591
2025-09-19 21:41:02,086 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 21:41:02] "GET / HTTP/1.1" 200 -
2025-09-19 21:41:04,892 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 21:41:04] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-09-19 21:41:15,234 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 21:41:15] "GET /process-manager HTTP/1.1" 200 -
2025-09-19 21:41:16,125 - __main__ - INFO - Executing ADB command: C:\platform-tools\adb.exe devices
2025-09-19 21:41:48,844 - __main__ - INFO - ADB command completed with return code: 0
2025-09-19 21:41:48,957 - __main__ - INFO - Found 0 connected devices
2025-09-19 21:41:48,979 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 21:41:48] "GET /devices HTTP/1.1" 200 -
2025-09-19 21:42:21,368 - __main__ - INFO - Executing ADB command: C:\platform-tools\adb.exe version
2025-09-19 21:42:21,620 - __main__ - INFO - ADB command completed with return code: 0
2025-09-19 21:42:21,627 - __main__ - INFO - Executing ADB command: C:\platform-tools\adb.exe devices
2025-09-19 21:42:21,753 - __main__ - INFO - ADB command completed with return code: 0
2025-09-19 21:42:21,754 - __main__ - INFO - Found 0 connected devices
2025-09-19 21:42:21,756 - __main__ - INFO - Debug ADB info requested: {'adb_path': 'C:\\platform-tools\\adb.exe', 'timestamp': '2025-09-19T21:42:21.368209', 'adb_version': {'return_code': 0, 'stdout': 'Android Debug Bridge version 1.0.41\nVersion 36.0.1-13811061\nInstalled as C:\\platform-tools\\adb.exe\nRunning on Windows 10.0.20231', 'stderr': ''}, 'devices': {'devices': [], 'error': None}}
2025-09-19 21:42:21,783 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 21:42:21] "GET /debug/adb HTTP/1.1" 200 -
2025-09-19 21:43:57,140 - __main__ - INFO - Executing ADB command: C:\platform-tools\adb.exe devices
2025-09-19 21:43:57,638 - __main__ - INFO - ADB command completed with return code: 0
2025-09-19 21:43:57,639 - __main__ - INFO - Found 0 connected devices
2025-09-19 21:43:57,640 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 21:43:57] "GET /devices HTTP/1.1" 200 -
2025-09-19 21:50:02,114 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\augment-projects\\Break\\break\\app.py', reloading
2025-09-19 21:50:04,712 - werkzeug - INFO -  * Restarting with stat
2025-09-19 21:50:50,678 - __main__ - INFO - Starting Android ADB Manager
2025-09-19 21:50:50,770 - __main__ - INFO - ADB Path: C:\platform-tools\adb.exe
2025-09-19 21:50:50,835 - __main__ - INFO - Upload Directory: C:\Users\<USER>\Documents\augment-projects\Break\break\uploads
2025-09-19 21:50:52,764 - __main__ - INFO - USB Device Manager initialized successfully
2025-09-19 21:50:53,199 - werkzeug - WARNING -  * Debugger is active!
2025-09-19 21:50:53,206 - werkzeug - INFO -  * Debugger PIN: 247-365-591
2025-09-19 21:53:51,576 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 21:53:51] "GET /process-manager HTTP/1.1" 200 -
2025-09-19 21:53:53,061 - __main__ - INFO - Executing ADB command: C:\platform-tools\adb.exe devices
2025-09-19 21:53:56,121 - __main__ - INFO - ADB command completed with return code: 0
2025-09-19 21:53:56,170 - __main__ - INFO - Found 0 connected devices
2025-09-19 21:53:56,292 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 21:53:56] "GET /devices HTTP/1.1" 200 -
2025-09-19 21:54:38,110 - __main__ - INFO - Executing ADB command: C:\platform-tools\adb.exe devices
2025-09-19 21:54:38,726 - __main__ - INFO - ADB command completed with return code: 0
2025-09-19 21:54:38,727 - __main__ - INFO - Found 0 connected devices
2025-09-19 21:54:38,727 - werkzeug - INFO - 127.0.0.1 - - [19/Sep/2025 21:54:38] "GET /devices HTTP/1.1" 200 -
2025-09-20 04:07:56,561 - __main__ - INFO - Starting Android ADB Manager
2025-09-20 04:07:56,607 - __main__ - INFO - ADB Path: C:\platform-tools\adb.exe
2025-09-20 04:07:56,608 - __main__ - INFO - Upload Directory: C:\Users\<USER>\Documents\augment-projects\Break\Break\uploads
2025-09-20 04:07:56,951 - __main__ - INFO - USB Device Manager initialized successfully
2025-09-20 04:20:08,711 - __main__ - INFO - Starting Android ADB Manager
2025-09-20 04:20:08,785 - __main__ - INFO - ADB Path: C:\platform-tools\adb.exe
2025-09-20 04:20:08,816 - __main__ - INFO - Upload Directory: C:\Users\<USER>\Documents\augment-projects\Break\Break\uploads
2025-09-20 04:20:10,808 - __main__ - INFO - USB Device Manager initialized successfully
2025-09-24 20:43:31,291 - __main__ - INFO - Starting Android ADB Manager
2025-09-24 20:43:31,348 - __main__ - INFO - ADB Path: adb (from PATH)
2025-09-24 20:43:31,349 - __main__ - INFO - Upload Directory: C:\Users\<USER>\Documents\augment-projects\Break\Break\uploads
2025-09-24 20:43:31,562 - __main__ - INFO - USB Device Manager initialized successfully
2025-09-25 05:33:23,190 - __main__ - INFO - Starting Android ADB Manager
2025-09-25 05:33:23,239 - __main__ - INFO - ADB Path: adb (from PATH)
2025-09-25 05:33:23,240 - __main__ - INFO - Upload Directory: C:\Users\<USER>\Documents\augment-projects\Break\Break\uploads
2025-09-25 05:33:23,417 - __main__ - INFO - USB Device Manager initialized successfully
