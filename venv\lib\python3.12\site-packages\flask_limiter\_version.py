
# This file was generated by 'versioneer.py' (0.22) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2023-05-03T16:44:55-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "b2f27ce0c7a12acc710254ab2d587cf106c01fc8",
 "version": "3.3.1"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
