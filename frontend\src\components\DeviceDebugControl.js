import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Switch,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  BugReport as BugReportIcon,
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { api } from '../services/api';

const DeviceDebugControl = ({ deviceSerial, onStatusChange }) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [debugStatus, setDebugStatus] = useState({
    debugging_enabled: false,
    device_authorized: false,
    developer_options: false,
    error_details: [],
  });

  useEffect(() => {
    fetchDebugStatus();
  }, [deviceSerial]);

  const fetchDebugStatus = async () => {
    try {
      const response = await api.get(`/api/devices/${deviceSerial}/debug/status`);
      setDebugStatus(response.data.status);
      setError(null);
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to fetch debug status');
    }
  };

  const handleToggleDebugging = async (enable) => {
    setLoading(true);
    setError(null);
    try {
      const endpoint = enable ? 'enable' : 'disable';
      const response = await api.post(`/api/devices/${deviceSerial}/debug/${endpoint}`);
      
      if (response.data.requires_confirmation) {
        setError('Please check your device for USB debugging authorization prompt');
      } else {
        setDebugStatus(response.data.status);
      }
      
      if (onStatusChange) {
        onStatusChange(response.data.status);
      }
    } catch (err) {
      setError(err.response?.data?.error || `Failed to ${enable ? 'enable' : 'disable'} debugging`);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    if (status) return <CheckCircleIcon color="success" />;
    return <ErrorIcon color="error" />;
  };

  return (
    <Card variant="outlined" sx={{ mb: 2 }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography variant="h6" component="div" sx={{ display: 'flex', alignItems: 'center' }}>
            <BugReportIcon sx={{ mr: 1 }} />
            USB Debugging Control
          </Typography>
          <Switch
            checked={debugStatus.debugging_enabled}
            onChange={(e) => handleToggleDebugging(e.target.checked)}
            disabled={loading}
            color="primary"
          />
        </Box>

        {loading && (
          <Box display="flex" justifyContent="center" my={2}>
            <CircularProgress size={24} />
          </Box>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <List dense>
          <ListItem>
            <ListItemIcon>
              {getStatusIcon(debugStatus.debugging_enabled)}
            </ListItemIcon>
            <ListItemText
              primary="USB Debugging"
              secondary={debugStatus.debugging_enabled ? 'Enabled' : 'Disabled'}
            />
          </ListItem>
          
          <ListItem>
            <ListItemIcon>
              {getStatusIcon(debugStatus.device_authorized)}
            </ListItemIcon>
            <ListItemText
              primary="Device Authorization"
              secondary={debugStatus.device_authorized ? 'Authorized' : 'Unauthorized'}
            />
          </ListItem>

          <ListItem>
            <ListItemIcon>
              {getStatusIcon(debugStatus.developer_options)}
            </ListItemIcon>
            <ListItemText
              primary="Developer Options"
              secondary={debugStatus.developer_options ? 'Enabled' : 'Disabled'}
            />
          </ListItem>
        </List>

        {debugStatus.error_details && debugStatus.error_details.length > 0 && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            <Typography variant="subtitle2">Issues to Resolve:</Typography>
            <List dense>
              {debugStatus.error_details.map((detail, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <WarningIcon color="warning" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary={detail} />
                </ListItem>
              ))}
            </List>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default DeviceDebugControl;