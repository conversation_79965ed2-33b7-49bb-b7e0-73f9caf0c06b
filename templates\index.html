<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Android Device Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .device-card {
            transition: transform 0.2s;
        }
        .device-card:hover {
            transform: translateY(-5px);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-online {
            background-color: #28a745;
        }
        .status-offline {
            background-color: #dc3545;
        }
        .status-unauthorized {
            background-color: #ffc107;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-mobile-alt me-2"></i>Android Device Manager
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#uploadModal">
                            <i class="fas fa-upload me-1"></i>Upload APK
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="refreshDevices">
                            <i class="fas fa-sync me-1"></i>Refresh Devices
                        </a>
                    </li>
                </ul>
                <span class="navbar-text">
                    <i class="fas fa-user me-1"></i>{{ username }}
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
    <!-- Batch Operations Section -->
    <div id="batchOperations" class="mb-4" style="display: none;">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Batch Operations (<span id="selectedDeviceCount">0</span> devices selected)</h5>
            </div>
            <div class="card-body">
                <div class="btn-group" role="group">
                    <button id="batchInstallBtn" class="btn btn-success">
                        <i class="fas fa-upload"></i> Install APK
                    </button>
                    <button id="batchUninstallBtn" class="btn btn-danger">
                        <i class="fas fa-trash-alt"></i> Uninstall App
                    </button>
                    <button id="batchWirelessBtn" class="btn btn-info">
                        <i class="fas fa-wifi"></i> Enable Wireless
                    </button>
                </div>
                <div id="batchResults" class="mt-3"></div>
            </div>
        </div>
    </div>
        <!-- Batch Install Modal -->
    <div class="modal fade" id="batchInstallModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Batch Install APK</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="batchInstallForm">
                        <div class="mb-3">
                            <label for="batchApkFile" class="form-label">Select APK File</label>
                            <input type="file" class="form-control" id="batchApkFile" accept=".apk" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="batchGrantPermissions" checked>
                            <label class="form-check-label" for="batchGrantPermissions">Grant All Permissions</label>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="batchReplaceExisting" checked>
                            <label class="form-check-label" for="batchReplaceExisting">Replace Existing App</label>
                        </div>
                        <button type="submit" class="btn btn-primary">Install</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row" id="deviceList">
            <!-- Device cards will be dynamically inserted here -->
        </div>
    </div>

    <!-- Upload Modal -->
    <div class="modal fade" id="uploadModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Upload APK</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="uploadForm">
                        <div class="mb-3">
                            <label class="form-label">Select APK File</label>
                            <input type="file" class="form-control" name="apk" accept=".apk" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Target Device</label>
                            <select class="form-select" name="device" required>
                                <option value="all">All Connected Devices</option>
                                <!-- Device options will be dynamically inserted here -->
                            </select>
                        </div>
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" name="grantPermissions" checked>
                            <label class="form-check-label">Auto-grant Permissions</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="uploadButton">Upload & Install</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Device Template -->
    <template id="deviceTemplate">
        <div class="col-md-4 mb-4">
            <div class="card device-card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>
                        <span class="status-indicator"></span>
                        <span class="device-name">Device Name</span>
                    </span>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            Actions
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item install-apk" href="#">Install APK</a></li>
                            <li><a class="dropdown-item view-apps" href="#">View Apps</a></li>
                            <li><a class="dropdown-item device-info" href="#">Device Info</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item enable-wireless" href="#">Enable Wireless</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item enable-debugging" href="#"><i class="fas fa-bug"></i> Enable Debugging</a></li>
                            <li><a class="dropdown-item check-debug-status" href="#"><i class="fas fa-stethoscope"></i> Debug Status</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <p class="card-text mb-2">
                        <small class="text-muted">Serial: </small>
                        <span class="device-serial">Unknown</span>
                    </p>
                    <p class="card-text mb-2">
                        <small class="text-muted">Model: </small>
                        <span class="device-model">Unknown</span>
                    </p>
                    <p class="card-text">
                        <small class="text-muted">Android Version: </small>
                        <span class="device-android-version">Unknown</span>
                    </p>
                    <div class="debug-status-indicators">
                        <!-- Debug status indicators will be dynamically inserted here -->
                    </div>
                    <div class="usb-debug-section mt-3 border-top pt-3">
                        <h6 class="mb-2"><i class="fas fa-usb"></i> USB Debugging</h6>
                        <div class="form-check form-switch">
                            <input class="form-check-input toggle-usb-debug" type="checkbox" role="switch">
                            <label class="form-check-label">Enable USB Debugging</label>
                        </div>
                        <div class="alert alert-warning mt-2 small">
                            <i class="fas fa-exclamation-triangle"></i> Warning: USB debugging allows direct access to device data. Only enable on trusted computers.
                        </div>
                        <div class="debug-status-info small text-muted mt-2">
                            <div><i class="fas fa-info-circle"></i> Status: <span class="debug-status">Checking...</span></div>
                            <div><i class="fas fa-shield-alt"></i> Authorization: <span class="debug-auth-status">Unknown</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/adb_debug.js') }}"></script>
    <!-- Add Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
</body>
</html>