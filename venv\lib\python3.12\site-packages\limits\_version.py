
# This file was generated by 'versioneer.py' (0.29) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-08-05T10:27:25-0700",
 "dirty": false,
 "error": null,
 "full-revisionid": "a5d3f81d1ead51586a42b8c843884603cb19bb7f",
 "version": "5.5.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
