// ADB Debugging Management Module
class ADBDebugManager {
    constructor() {
        this.initializeEventListeners();
        this.initializeDebugToggles();
    }

    initializeEventListeners() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('.enable-debugging')) {
                const serial = e.target.closest('.device-card').dataset.serial;
                this.enableDebugging(serial);
            } else if (e.target.matches('.check-debug-status')) {
                const serial = e.target.closest('.device-card').dataset.serial;
                this.checkDebugStatus(serial);
            }
        });

        // Listen for USB debugging toggle changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('.toggle-usb-debug')) {
                const deviceCard = e.target.closest('.device-card');
                const serial = deviceCard.dataset.serial;
                const enabled = e.target.checked;
                this.toggleUsbDebugging(serial, enabled, deviceCard);
            }
        });
    }

    initializeDebugToggles() {
        // Update debug status for all devices periodically
        setInterval(() => {
            document.querySelectorAll('.device-card').forEach(card => {
                const serial = card.dataset.serial;
                this.updateDebugStatus(serial, card);
            });
        }, 10000); // Check every 10 seconds
    }

    async toggleUsbDebugging(serial, enabled, deviceCard) {
        const toggle = deviceCard.querySelector('.toggle-usb-debug');
        const statusSpan = deviceCard.querySelector('.debug-status');
        const authSpan = deviceCard.querySelector('.debug-auth-status');

        try {
            if (enabled) {
                // Try to enable USB debugging
                const response = await fetch(`/api/devices/${serial}/debug/enable`, {
                    method: 'POST'
                });
                const data = await response.json();

                if (data.success) {
                    statusSpan.textContent = 'Enabled';
                    statusSpan.className = 'debug-status text-success';
                    
                    if (data.requires_confirmation) {
                        authSpan.textContent = 'Needs Authorization';
                        authSpan.className = 'debug-auth-status text-warning';
                        
                        Swal.fire({
                            title: 'Device Authorization Required',
                            html: `
                                <div class="alert alert-info">
                                    <strong>Please check your device screen and:</strong>
                                    <ol class="text-start">
                                        <li>Unlock your device if locked</li>
                                        <li>Look for the "Allow USB debugging" dialog</li>
                                        <li>Check "Always allow from this computer"</li>
                                        <li>Tap "Allow" to authorize</li>
                                    </ol>
                                </div>
                            `,
                            icon: 'info',
                            showConfirmButton: true
                        });
                    }
                } else {
                    throw new Error(data.message || 'Failed to enable USB debugging');
                }
            } else {
                // Disable USB debugging
                const response = await fetch(`/api/devices/${serial}/debug/disable`, {
                    method: 'POST'
                });
                const data = await response.json();

                if (data.success) {
                    statusSpan.textContent = 'Disabled';
                    statusSpan.className = 'debug-status text-danger';
                    authSpan.textContent = 'N/A';
                    authSpan.className = 'debug-auth-status text-muted';
                } else {
                    throw new Error(data.message || 'Failed to disable USB debugging');
                }
            }
        } catch (error) {
            console.error('USB debugging toggle error:', error);
            toggle.checked = !enabled; // Revert toggle state
            Swal.fire({
                title: 'Error',
                text: error.message,
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    }

    async updateDebugStatus(serial, deviceCard) {
        try {
            const response = await fetch(`/api/devices/${serial}/debug/status`);
            const data = await response.json();

            if (response.ok) {
                const toggle = deviceCard.querySelector('.toggle-usb-debug');
                const statusSpan = deviceCard.querySelector('.debug-status');
                const authSpan = deviceCard.querySelector('.debug-auth-status');

                toggle.checked = data.debugging_enabled;
                
                statusSpan.textContent = data.debugging_enabled ? 'Enabled' : 'Disabled';
                statusSpan.className = `debug-status ${data.debugging_enabled ? 'text-success' : 'text-danger'}`;

                authSpan.textContent = data.device_authorized ? 'Authorized' : 'Unauthorized';
                authSpan.className = `debug-auth-status ${data.device_authorized ? 'text-success' : 'text-warning'}`;
            }
        } catch (error) {
            console.error('Failed to update debug status:', error);
        }
    }
    }

    async enableDebugging(serial) {
        try {
            const response = await fetch(`/api/devices/${serial}/debug/enable`, {
                method: 'POST'
            });
            const data = await response.json();

            if (response.ok) {
                Swal.fire({
                    title: 'ADB Debugging',
                    html: `
                        <div class="debug-instructions">
                            <p>${data.message}</p>
                            ${data.requires_confirmation ? `
                                <div class="alert alert-info">
                                    <strong>Action Required:</strong> Please check your device screen and:
                                    <ol>
                                        <li>Unlock your device if locked</li>
                                        <li>Look for the "Allow USB debugging" dialog</li>
                                        <li>Check "Always allow from this computer" (recommended)</li>
                                        <li>Click "Allow" to authorize debugging</li>
                                    </ol>
                                </div>
                            ` : ''}
                        </div>
                    `,
                    icon: data.success ? 'success' : 'info',
                    showConfirmButton: true,
                    confirmButtonText: 'OK'
                });
            } else {
                throw new Error(data.error || 'Failed to enable debugging');
            }
        } catch (error) {
            Swal.fire({
                title: 'Error',
                text: error.message,
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    }

    async checkDebugStatus(serial) {
        try {
            const response = await fetch(`/api/devices/${serial}/debug/status`);
            const data = await response.json();

            if (response.ok) {
                const statusHtml = `
                    <div class="debug-status">
                        <table class="table">
                            <tbody>
                                <tr>
                                    <th>Debug Status:</th>
                                    <td>
                                        <span class="badge ${data.debugging_enabled ? 'bg-success' : 'bg-danger'}">
                                            ${data.debugging_enabled ? 'Enabled' : 'Disabled'}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Authorization:</th>
                                    <td>
                                        <span class="badge ${data.device_authorized ? 'bg-success' : 'bg-warning'}">
                                            ${data.device_authorized ? 'Authorized' : 'Unauthorized'}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Developer Options:</th>
                                    <td>
                                        <span class="badge ${data.developer_options ? 'bg-success' : 'bg-danger'}">
                                            ${data.developer_options ? 'Enabled' : 'Disabled'}
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        ${!data.debugging_enabled || !data.device_authorized ? `
                            <div class="alert alert-info mt-3">
                                <h5>Troubleshooting Steps:</h5>
                                <ol>
                                    <li>Enable Developer Options:
                                        <ul>
                                            <li>Go to Settings > About phone</li>
                                            <li>Tap "Build number" 7 times</li>
                                        </ul>
                                    </li>
                                    <li>Enable USB Debugging:
                                        <ul>
                                            <li>Go to Settings > System > Developer options</li>
                                            <li>Find and enable "USB debugging"</li>
                                        </ul>
                                    </li>
                                    <li>Check USB Connection:
                                        <ul>
                                            <li>Use a high-quality USB cable</li>
                                            <li>Try different USB ports</li>
                                            <li>Set USB mode to "File Transfer" or "PTP"</li>
                                        </ul>
                                    </li>
                                </ol>
                            </div>
                        ` : ''}
                    </div>
                `;

                Swal.fire({
                    title: 'ADB Debug Status',
                    html: statusHtml,
                    width: '600px',
                    showConfirmButton: true,
                    confirmButtonText: 'Close'
                });
            } else {
                throw new Error(data.error || 'Failed to get debug status');
            }
        } catch (error) {
            Swal.fire({
                title: 'Error',
                text: error.message,
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    }
}

// Initialize ADB Debug Manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adbDebugManager = new ADBDebugManager();
});
