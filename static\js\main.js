// Main JavaScript file for Android Device Manager

class DeviceManager {
    constructor() {
        this.selectedDevices = new Set();
        this.debugManager = new DebugManager();
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Refresh devices periodically
        setInterval(() => this.refreshDevices(), 5000);

        // Device action handlers
        document.addEventListener('click', (e) => {
            const card = e.target.closest('.card');
            if (!card) return;

            const serial = card.querySelector('.device-checkbox').dataset.serial;

            if (e.target.matches('.device-info')) {
                this.showDeviceInfo(serial);
            } else if (e.target.matches('.install-apk')) {
                this.showInstallModal(serial);
            } else if (e.target.matches('.view-apps')) {
                this.showPackages(serial);
            } else if (e.target.matches('.enable-wireless')) {
                this.enableWirelessADB(serial);
            }
        });

        // Device selection handling
        document.addEventListener('click', (e) => {
            if (e.target.matches('.device-checkbox')) {
                const serial = e.target.dataset.serial;
                if (e.target.checked) {
                    this.selectedDevices.add(serial);
                } else {
                    this.selectedDevices.delete(serial);
                }
                this.updateBatchOperationsUI();
            }
        });

        // Batch operations buttons
        document.getElementById('batchInstallBtn').addEventListener('click', () => this.showBatchInstallModal());
        document.getElementById('batchUninstallBtn').addEventListener('click', () => this.showBatchUninstallModal());
        document.getElementById('batchWirelessBtn').addEventListener('click', () => this.enableBatchWireless());
        // Device selection handling
        document.addEventListener('click', (e) => {
            if (e.target.matches('.device-checkbox')) {
                const serial = e.target.dataset.serial;
                if (e.target.checked) {
                    this.selectedDevices.add(serial);
                } else {
                    this.selectedDevices.delete(serial);
                }
                this.updateBatchOperationsUI();
            }
        });

        // Batch operations buttons
        document.getElementById('batchInstallBtn').addEventListener('click', () => this.showBatchInstallModal());
        document.getElementById('batchUninstallBtn').addEventListener('click', () => this.showBatchUninstallModal());
        document.getElementById('batchWirelessBtn').addEventListener('click', () => this.enableBatchWireless());
    }

    updateBatchOperationsUI() {
        const hasSelected = this.selectedDevices.size > 0;
        document.getElementById('batchOperations').style.display = hasSelected ? 'block' : 'none';
        document.getElementById('selectedDeviceCount').textContent = this.selectedDevices.size;
    }

    async showBatchInstallModal() {
        const modal = new bootstrap.Modal(document.getElementById('batchInstallModal'));
        modal.show();

        document.getElementById('batchInstallForm').onsubmit = async (e) => {
            e.preventDefault();
            const formData = new FormData();
            const apkFile = document.getElementById('batchApkFile').files[0];
            if (!apkFile) {
                alert('Please select an APK file');
                return;
            }

            formData.append('apk', apkFile);
            formData.append('devices', JSON.stringify(Array.from(this.selectedDevices)));
            formData.append('grant_permissions', document.getElementById('batchGrantPermissions').checked);
            formData.append('replace_existing', document.getElementById('batchReplaceExisting').checked);

            try {
                const response = await fetch('/api/batch/install', {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                });

                const result = await response.json();
                this.displayBatchResults('Installation Results', result);
                modal.hide();
            } catch (error) {
                console.error('Batch installation failed:', error);
                alert('Batch installation failed. Check console for details.');
            }
        };
    }

    async showBatchUninstallModal() {
        const packageName = prompt('Enter package name to uninstall:');
        if (!packageName) return;

        try {
            const response = await fetch('/api/batch/uninstall', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    package_name: packageName,
                    devices: Array.from(this.selectedDevices),
                    keep_data: false
                }),
                credentials: 'same-origin'
            });

            const result = await response.json();
            this.displayBatchResults('Uninstallation Results', result);
        } catch (error) {
            console.error('Batch uninstallation failed:', error);
            alert('Batch uninstallation failed. Check console for details.');
        }
    }

    async enableBatchWireless() {
        try {
            const response = await fetch('/api/batch/enable_wireless', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    devices: Array.from(this.selectedDevices)
                }),
                credentials: 'same-origin'
            });

            const result = await response.json();
            this.displayBatchResults('Wireless ADB Results', result);
        } catch (error) {
            console.error('Batch wireless enable failed:', error);
            alert('Failed to enable wireless ADB. Check console for details.');
        }
    }

    displayBatchResults(title, results) {
        const resultsDiv = document.createElement('div');
        resultsDiv.className = 'batch-results mt-3';
        let html = `<h5>${title}</h5><ul class="list-group">`;

        for (const [serial, result] of Object.entries(results)) {
            const success = result.success;
            const message = result.message || result.error || (result.details ? JSON.stringify(result.details) : 'Completed');
            html += `
                <li class="list-group-item ${success ? 'list-group-item-success' : 'list-group-item-danger'}">
                    <strong>${serial}:</strong> ${message}
                </li>`;
        }

        html += '</ul>';
        resultsDiv.innerHTML = html;

        const existingResults = document.querySelector('.batch-results');
        if (existingResults) {
            existingResults.remove();
        }
        document.getElementById('batchResults').appendChild(resultsDiv);
    }
    constructor() {
        this.devices = new Map();
        this.initializeEventListeners();
        this.refreshDevices();
        // Start periodic refresh
        setInterval(() => this.refreshDevices(), 30000);
    }

    initializeEventListeners() {
        // Refresh button
        document.getElementById('refreshDevices').addEventListener('click', () => this.refreshDevices());

        // Upload form
        document.getElementById('uploadButton').addEventListener('click', () => this.handleUpload());

        // Device list event delegation
        document.getElementById('deviceList').addEventListener('click', (e) => {
            const deviceCard = e.target.closest('.device-card');
            if (!deviceCard) return;

            const serial = deviceCard.dataset.serial;
            
            if (e.target.classList.contains('install-apk')) {
                this.showUploadModal(serial);
            } else if (e.target.classList.contains('view-apps')) {
                this.viewInstalledApps(serial);
            } else if (e.target.classList.contains('device-info')) {
                this.showDeviceInfo(serial);
            } else if (e.target.classList.contains('enable-wireless')) {
                this.enableWirelessAdb(serial);
            }
        });
    }

    async refreshDevices() {
        try {
            const response = await fetch('/api/devices');
            const data = await response.json();
            
            if (response.ok) {
                this.updateDeviceList(data.devices);
            } else {
                this.showError('Failed to fetch devices', data.error);
            }
        } catch (error) {
            this.showError('Error', 'Failed to connect to server');
        }
    }

    updateDeviceList(devices) {
        const container = document.getElementById('deviceList');
        container.innerHTML = '';

        devices.forEach(device => {
            const card = document.createElement('div');
            card.className = 'col-md-6 col-lg-4 mb-4';
            card.innerHTML = `
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="form-check">
                            <input class="form-check-input device-checkbox" type="checkbox" data-serial="${device.serial}" id="checkbox-${device.serial}">
                            <label class="form-check-label" for="checkbox-${device.serial}">
                                ${device.model || 'Unknown Device'}
                            </label>
                        </div>
                        <span class="badge ${device.state === 'device' ? 'bg-success' : 'bg-danger'}">${device.serial}</span>
                    </div>
                    <div class="card-body">
                        <div class="device-info">
                            <p><strong>Model:</strong> ${device.model || 'Unknown'}</p>
                            <p><strong>Android Version:</strong> ${device.android_version || 'Unknown'}</p>
                            <p><strong>Status:</strong> ${device.state}</p>
                        </div>
                        <div class="btn-group w-100 mt-3">
                            <button class="btn btn-primary btn-sm device-info">
                                <i class="fas fa-info-circle"></i> Info
                            </button>
                            <button class="btn btn-success btn-sm install-apk">
                                <i class="fas fa-upload"></i> Install
                            </button>
                            <button class="btn btn-info btn-sm view-apps">
                                <i class="fas fa-list"></i> Apps
                            </button>
                            <button class="btn btn-warning btn-sm enable-wireless">
                                <i class="fas fa-wifi"></i> Wireless
                            </button>
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(card);
        });
    }

    async handleUpload() {
        const form = document.getElementById('uploadForm');
        const formData = new FormData(form);

        try {
            const response = await fetch('/api/install', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (response.ok) {
                this.showSuccess('Upload Successful', 'APK has been uploaded and installation started');
                $('#uploadModal').modal('hide');
            } else {
                this.showError('Upload Failed', data.error);
            }
        } catch (error) {
            this.showError('Error', 'Failed to upload APK');
        }
    }

    async viewInstalledApps(serial) {
        try {
            const response = await fetch(`/api/devices/${serial}/packages`);
            const data = await response.json();

            if (response.ok) {
                let html = '<div class="list-group">';
                data.packages.forEach(pkg => {
                    html += `
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">${pkg.name}</h6>
                                <small class="text-muted">Version: ${pkg.version || 'Unknown'}</small>
                            </div>
                            <button class="btn btn-sm btn-danger uninstall-app" data-package="${pkg.name}">
                                Uninstall
                            </button>
                        </div>
                    `;
                });
                html += '</div>';

                Swal.fire({
                    title: 'Installed Applications',
                    html: html,
                    width: '600px',
                    showConfirmButton: false,
                    showCloseButton: true
                });
            } else {
                this.showError('Error', data.error);
            }
        } catch (error) {
            this.showError('Error', 'Failed to fetch installed applications');
        }
    }

    async showDeviceInfo(serial) {
        try {
            const response = await fetch(`/api/devices/${serial}/info`);
            const data = await response.json();

            if (response.ok) {
                const info = data.device_info;
                const health = info.health || {};
                let html = `
                    <div class="table-responsive">
                        <h4>Device Information</h4>
                        <table class="table table-sm">
                            <tbody>
                                <tr><th>Model</th><td>${info.model || 'Unknown'}</td></tr>
                                <tr><th>Android Version</th><td>${info.android_version || 'Unknown'}</td></tr>
                                <tr><th>API Level</th><td>${info.api_level || 'Unknown'}</td></tr>
                                <tr><th>Manufacturer</th><td>${info.manufacturer || 'Unknown'}</td></tr>
                                <tr><th>Brand</th><td>${info.brand || 'Unknown'}</td></tr>
                                <tr><th>CPU Architecture</th><td>${info.cpu_abi || 'Unknown'}</td></tr>
                                <tr><th>Serial</th><td>${info.serial || 'Unknown'}</td></tr>
                            </tbody>
                        </table>

                        <h4 class="mt-4">Health Information</h4>
                        <table class="table table-sm">
                            <tbody>
                                <tr>
                                    <th>Battery Level</th>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar ${health.battery_level > 20 ? 'bg-success' : 'bg-danger'}" 
                                                role="progressbar" 
                                                style="width: ${health.battery_level || 0}%" 
                                                aria-valuenow="${health.battery_level || 0}" 
                                                aria-valuemin="0" 
                                                aria-valuemax="100">
                                                ${health.battery_level || 0}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr><th>Battery Status</th><td>${health.battery_status || 'Unknown'}</td></tr>
                                <tr><th>Battery Temperature</th><td>${health.battery_temp ? health.battery_temp + '°C' : 'Unknown'}</td></tr>
                                <tr><th>Total RAM</th><td>${health.total_ram || 'Unknown'}</td></tr>
                                <tr><th>Free RAM</th><td>${health.free_ram || 'Unknown'}</td></tr>
                                <tr><th>Used RAM</th><td>${health.used_ram || 'Unknown'}</td></tr>
                                <tr><th>CPU Cores</th><td>${health.cpu_cores || 'Unknown'}</td></tr>
                                <tr><th>Running Processes</th><td>${health.process_count || 'Unknown'}</td></tr>
                            </tbody>
                        </table>

                        ${health.top_cpu_processes ? `
                            <h4 class="mt-4">Top CPU Processes</h4>
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Process</th>
                                        <th>CPU Usage</th>
                                        <th>PID</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${health.top_cpu_processes.map(proc => `
                                        <tr>
                                            <td>${proc.process}</td>
                                            <td>${proc.cpu_usage}%</td>
                                            <td>${proc.pid}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        ` : ''}
                    </div>
                `;

                Swal.fire({
                    title: 'Device Information',
                    html: html,
                    width: '600px',
                    showConfirmButton: false,
                    showCloseButton: true
                });
            } else {
                this.showError('Error', data.error);
            }
        } catch (error) {
            this.showError('Error', 'Failed to fetch device information');
        }
    }

    async enableWirelessAdb(serial) {
        try {
            const response = await fetch(`/api/devices/${serial}/wireless`, {
                method: 'POST'
            });
            const data = await response.json();

            if (response.ok) {
                this.showSuccess('Wireless ADB Enabled', 
                    `Device will disconnect and reconnect wirelessly. IP: ${data.ip_address}`);
            } else {
                this.showError('Error', data.error);
            }
        } catch (error) {
            this.showError('Error', 'Failed to enable wireless ADB');
        }
    }

    showError(title, message) {
        Swal.fire({
            title: title,
            text: message,
            icon: 'error',
            confirmButtonText: 'OK'
        });
    }

    showSuccess(title, message) {
        Swal.fire({
            title: title,
            text: message,
            icon: 'success',
            confirmButtonText: 'OK'
        });
    }
}

// Initialize device manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.deviceManager = new DeviceManager();
});